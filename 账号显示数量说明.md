# 账号显示数量配置说明

## 🔍 问题解答

### 为什么只显示10个账号？

之前为了避免API超时，我临时限制了只显示前10个账号。现在已经优化，支持显示更多账号。

## ✅ 现在的解决方案

### 1. 灵活的数量选择
在Web界面中，您可以选择显示的账号数量：
- **10个账号**：快速加载，适合测试
- **20个账号**：平衡速度和数量
- **50个账号**：默认选项，推荐使用
- **100个账号**：显示更多账号
- **全部账号**：显示所有144个账号

### 2. 配置文件设置
在 `兑换配置.py` 中可以调整：

```python
# ==================== Web界面设置 ====================
# 最大显示账号数量（防止页面卡顿）
MAX_ACCOUNTS_DISPLAY = 100

# 默认显示账号数量
DEFAULT_ACCOUNTS_DISPLAY = 50

# 每批处理账号数量（异步加载时使用）
BATCH_SIZE = 10
```

### 3. 智能加载机制
- **快速显示**：先显示基本信息（1-2秒）
- **异步查询**：后台逐个查询详细信息
- **实时更新**：查询完成后立即更新界面
- **自动过滤**：只显示符合条件的账号（天数≥31天且未兑换）

## 🎯 使用方法

### 1. 选择显示数量
1. 在"可用账号管理"区域
2. 找到"显示账号数量"下拉菜单
3. 选择您需要的数量
4. 点击"🔄 刷新账号"

### 2. 查看统计信息
界面会显示：
```
📊 总共 144 个账号，当前显示 50 个账号
```

### 3. 等待加载完成
- 账号卡片会显示"查询中..."
- 逐个更新为实际的天数和状态
- 不符合条件的账号会自动移除

## ⚙️ 性能优化

### 1. 分批处理
- 每次只处理10个账号（BATCH_SIZE）
- 避免同时发起过多请求
- 减少服务器压力

### 2. 智能过滤
- 只显示天数≥31天的账号
- 只显示未兑换过的账号
- 自动移除无效账号

### 3. 缓存机制
- 避免重复查询相同账号
- 提高响应速度

## 📊 推荐设置

### 日常使用
- **50个账号**：平衡速度和数量，推荐
- 加载时间：约2-5分钟完成所有查询

### 快速测试
- **10个账号**：快速验证功能
- 加载时间：约30秒-1分钟

### 大批量处理
- **100个账号**：处理更多账号
- 加载时间：约5-10分钟

### 全部账号
- **全部账号**：显示所有144个账号
- 加载时间：约10-15分钟
- ⚠️ 注意：可能会比较慢，建议在网络良好时使用

## 🔧 自定义配置

### 修改默认显示数量
编辑 `兑换配置.py`：
```python
DEFAULT_ACCOUNTS_DISPLAY = 30  # 改为30个
```

### 修改最大显示数量
```python
MAX_ACCOUNTS_DISPLAY = 200  # 改为200个
```

### 修改批处理大小
```python
BATCH_SIZE = 5  # 改为每批5个，更慢但更稳定
```

## 🚨 注意事项

### 1. 网络影响
- 网络不稳定时建议选择较少的账号数量
- 查询失败的账号会自动跳过

### 2. 服务器限制
- 小米服务器可能有频率限制
- 过快的请求可能被拒绝

### 3. 浏览器性能
- 显示过多账号可能影响页面性能
- 建议根据设备性能选择合适的数量

## 🎉 总结

现在您可以：
- ✅ 自由选择显示的账号数量（10-144个）
- ✅ 查看所有账号的统计信息
- ✅ 享受智能的异步加载体验
- ✅ 根据需要调整配置参数

不再受限于只显示10个账号！您可以根据实际需要选择合适的数量。
