#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试cookies获取和使用
"""

import sys
import os
import requests
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web_server import get_xiaomi_cookies, load_accounts_from_file, mask_user_id

def test_cookies_step_by_step():
    print("🧪 逐步测试cookies获取和使用")
    print("=" * 60)
    
    # 加载账号
    accounts = load_accounts_from_file()
    if not accounts:
        print("❌ 未找到账号信息")
        return
    
    account = accounts[0]
    print(f"📱 测试账号: {account['phone']}")
    print(f"👤 用户ID: {mask_user_id(account['userId'])}")
    print(f"🔑 passToken长度: {len(account['passToken'])}")
    
    # 步骤1: 手动获取cookies
    print("\n🔐 步骤1: 手动获取cookies")
    login_url = 'https://account.xiaomi.com/pass/serviceLogin?callback=https%3A%2F%2Fapi.jr.airstarfinance.net%2Fsts%3Fsign%3D1dbHuyAmee0NAZ2xsRw5vhdVQQ8%253D%26followup%3Dhttps%253A%252F%252Fm.jr.airstarfinance.net%252Fmp%252Fapi%252Flogin%253Ffrom%253Dmipay_indexicon_TVcard%2526deepLinkEnable%253Dfalse%2526requestUrl%253Dhttps%25253A%25252F%25252Fm.jr.airstarfinance.net%25252Fmp%25252Factivity%25252FvideoActivity%25253Ffrom%25253Dmipay_indexicon_TVcard%252526_noDarkMode%25253Dtrue%252526_transparentNaviBar%25253Dtrue%252526cUserId%25253Dusyxgr5xjumiQLUoAKTOgvi858Q%252526_statusBarHeight%25253D137&sid=jrairstar&_group=DEFAULT&_snsNone=true&_loginType=ticket'
    
    headers = {
        'user-agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2012K11AC Build/UKQ1.230804.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/100.0.4896.127 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'cookie': f'passToken={account["passToken"]}; userId={account["userId"]};'
    }
    
    try:
        session = requests.Session()
        resp = session.get(url=login_url, headers=headers, verify=False, timeout=10)
        
        print(f"🔐 登录响应状态: {resp.status_code}")
        print(f"🔐 响应URL: {resp.url}")
        
        cookies = session.cookies.get_dict()
        print(f"🔐 获取到的cookies: {list(cookies.keys())}")
        print(f"🔐 cookies详情:")
        for key, value in cookies.items():
            print(f"   {key}: {value[:50]}..." if len(value) > 50 else f"   {key}: {value}")
        
        if 'cUserId' in cookies and 'serviceToken' in cookies:
            result_cookies = f"cUserId={cookies.get('cUserId')};jrairstar_serviceToken={cookies.get('serviceToken')}"
            print(f"✅ cookies获取成功")
            print(f"🔐 最终cookies: {result_cookies}")
        else:
            print(f"❌ cookies获取失败 - 缺少必要字段")
            return None
            
    except Exception as e:
        print(f"❌ cookies获取异常: {str(e)}")
        return None
    
    # 步骤2: 测试cookies是否有效
    print("\n📊 步骤2: 测试cookies有效性")
    test_url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/queryUserGoldRichSum?app=com.mipay.wallet&deviceType=2&system=1&visitEnvironment=2&userExtra={"platformType":1,"com.miui.player":"********","com.miui.video":"v2024090290(MiVideo-UN)","com.mipay.wallet":"6.83.0.5175.2256"}&activityCode=2211-videoWelfare'
    
    test_headers = {
        'Host': 'm.jr.airstarfinance.net',
        'User-Agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2012K11AC Build/UKQ1.230804.001; AppBundle/com.mipay.wallet; AppVersionName/6.89.1.5275.2323; AppVersionCode/20577595; MiuiVersion/stable-V816.0.13.0.UMNCNXM; DeviceId/alioth; NetworkType/WIFI; mix_version; WebViewVersion/*********) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'Cookie': result_cookies
    }
    
    try:
        test_resp = requests.get(test_url, headers=test_headers, verify=False, timeout=10)
        print(f"📊 测试响应状态: {test_resp.status_code}")
        
        if test_resp.status_code == 200:
            try:
                test_data = test_resp.json()
                print(f"📊 测试响应数据: {test_data}")
                
                if test_data.get('code') == 0:
                    total_days = int(test_data['value']) / 100
                    print(f"✅ cookies有效！总天数: {total_days}")
                else:
                    print(f"⚠️ API返回错误: {test_data}")
            except Exception as e:
                print(f"⚠️ 解析响应失败: {str(e)}")
                print(f"📄 原始响应: {test_resp.text[:200]}...")
        else:
            print(f"❌ 请求失败，状态码: {test_resp.status_code}")
            print(f"📄 响应内容: {test_resp.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 测试请求异常: {str(e)}")
    
    # 步骤3: 测试兑换接口
    print("\n🎬 步骤3: 测试兑换接口")
    exchange_url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/convertGoldRich?prizeCode=LSXD_PRIZE1263&activityCode=2211-videoWelfare&phone=18348185287&isNfcPhone=false&channel=exchange&deviceType=2&system=1&visitEnvironment=2&userExtra=%7B%22platformType%22:1,%22com.miui.player%22:%22********%22,%22com.miui.video%22:%22v2024090290(MiVideo-UN)%22,%22com.mipay.wallet%22:%226.83.0.5175.2256%22%7D'
    
    try:
        exchange_resp = requests.get(exchange_url, headers=test_headers, verify=False, timeout=10)
        print(f"🎬 兑换响应状态: {exchange_resp.status_code}")
        
        if exchange_resp.status_code == 200:
            try:
                exchange_data = exchange_resp.json()
                print(f"🎬 兑换响应数据:")
                import json
                print(json.dumps(exchange_data, indent=2, ensure_ascii=False))
                
                if exchange_data.get('code') == 0 and exchange_data.get('success') == True:
                    print(f"🎉 兑换成功！")
                    prize_info = exchange_data.get('value', {}).get('prizeInfo', {})
                    print(f"🎁 奖品描述: {prize_info.get('prizeDesc', '未知')}")
                else:
                    print(f"❌ 兑换失败: {exchange_data.get('error', '未知错误')}")
            except Exception as e:
                print(f"⚠️ 解析兑换响应失败: {str(e)}")
                print(f"📄 原始响应: {exchange_resp.text[:200]}...")
        else:
            print(f"❌ 兑换请求失败，状态码: {exchange_resp.status_code}")
            print(f"📄 响应内容: {exchange_resp.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 兑换请求异常: {str(e)}")

if __name__ == "__main__":
    test_cookies_step_by_step()
