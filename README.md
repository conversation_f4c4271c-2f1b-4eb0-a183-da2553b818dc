# 小米钱包自动任务脚本 - 更新说明

## 主要更新

### 1. 环境变量读取方式改变
- **之前**: 从环境变量 `xmqb` 读取账号信息
- **现在**: 从 `ck.txt` 文件读取账号信息

### 2. 新增任务开关
- 添加了 `DO_TASKS` 开关，可以控制是否执行每日任务
- 当设置为 `False` 时，只查询账户信息，不执行任务

## 配置说明

### 功能开关设置
在脚本中找到以下配置项：

```python
# ==================== 功能设置 ====================
# 总开关：是否开启自动兑换功能 (True/False)
AUTO_EXCHANGE_SWITCH = False

# 兑换会员类型 (目前支持: iqiyi/tencent/youku/mango)
EXCHANGE_TYPE = "iqiyi"

# 任务开关：是否执行每日任务 (True/False)
DO_TASKS = True

# =======================================================
```

### ck.txt 文件格式
文件中每行格式为：
```
手机号----cookies----其他信息----加密信息----passToken; userId=用户ID; psecurity=xxx
```

示例：
```
19202517653----cUserId=xxx; jrairstar_serviceToken=xxx----musicVersion=xxx----加密信息----passToken=xxx; userId=3057660661; psecurity=xxx
```

## 使用方法

1. 确保 `ck.txt` 文件存在于脚本同目录下
2. 根据需要修改脚本中的功能开关
3. 运行脚本：
   ```bash
   python "小米[通知版]钱包(1).py"
   ```

## 功能说明

### DO_TASKS 开关
- `True`: 执行每日任务，获取收益
- `False`: 仅查询账户信息，不执行任务

### AUTO_EXCHANGE_SWITCH 开关
- `True`: 开启自动兑换功能（需满足条件：总天数≥7天且从未兑换过，且当前时间为10点）
- `False`: 关闭自动兑换功能

## 输出信息

脚本会显示：
- 找到的账号数量
- 各功能开关状态
- 每个账号的处理结果（包括手机号脱敏显示）
- 总天数、今日收益等信息
- 任务执行状态和兑换状态

## 注意事项

1. 确保 `ck.txt` 文件格式正确
2. 手机号和用户ID会进行脱敏显示保护隐私
3. 兑换功能需要满足特定条件才会执行
4. 建议在测试时先将 `DO_TASKS` 设为 `False` 验证账号有效性
