<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .result {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            border-color: #f8bbd9;
        }
        .success {
            color: #2e7d32;
            background: #e8f5e9;
            border-color: #c8e6c9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 小米钱包API测试</h1>
        
        <div>
            <button class="btn" onclick="testStock()">测试库存API</button>
            <button class="btn" onclick="testAccounts()">测试账号API</button>
            <button class="btn" onclick="testAccountDetail()">测试账号详情API</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        let currentUserId = null;
        
        function addResult(title, content, type = 'result') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${title}</strong>\n${content}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testStock() {
            addResult('📦 测试库存API', '正在请求...', 'loading');
            
            try {
                const response = await fetch('/api/stock');
                const data = await response.json();
                
                if (response.ok) {
                    addResult('📦 库存API测试结果', JSON.stringify(data, null, 2), 'success');
                } else {
                    addResult('📦 库存API测试失败', JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                addResult('📦 库存API测试错误', error.message, 'error');
            }
        }
        
        async function testAccounts() {
            addResult('👥 测试账号API', '正在请求...', 'loading');
            
            try {
                const response = await fetch('/api/accounts');
                const data = await response.json();
                
                if (response.ok) {
                    if (data.length > 0) {
                        currentUserId = data[0].userId;
                        addResult('👥 账号API测试结果', `找到 ${data.length} 个账号\n${JSON.stringify(data, null, 2)}`, 'success');
                    } else {
                        addResult('👥 账号API测试结果', '未找到账号', 'success');
                    }
                } else {
                    addResult('👥 账号API测试失败', JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                addResult('👥 账号API测试错误', error.message, 'error');
            }
        }
        
        async function testAccountDetail() {
            if (!currentUserId) {
                addResult('🔍 账号详情API测试', '请先运行账号API测试获取用户ID', 'error');
                return;
            }
            
            addResult('🔍 测试账号详情API', `正在查询用户 ${currentUserId} 的详情...`, 'loading');
            
            try {
                const response = await fetch(`/api/account/${currentUserId}/details`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult('🔍 账号详情API测试结果', JSON.stringify(data, null, 2), 'success');
                } else {
                    addResult('🔍 账号详情API测试失败', JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                addResult('🔍 账号详情API测试错误', error.message, 'error');
            }
        }
        
        // 页面加载时自动测试库存API
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 页面加载完成', '可以开始测试API功能', 'success');
        });
    </script>
</body>
</html>
