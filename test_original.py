#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试原版兑换功能
"""

import sys
import os

# 导入原版模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_original_exchange():
    print("🧪 测试原版兑换功能")
    print("=" * 50)
    
    # 导入原版的类和函数
    import importlib.util
    spec = importlib.util.spec_from_file_location("xiaomi_wallet", "小米[通知版]钱包(1).py")
    xiaomi_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(xiaomi_module)

    RNL = xiaomi_module.RNL
    load_accounts_from_file = xiaomi_module.load_accounts_from_file
    
    # 加载账号
    accounts, phones = load_accounts_from_file()
    if not accounts:
        print("❌ 未找到账号信息")
        return

    print(f"✅ 找到 {len(accounts)} 个账号")

    # 使用第一个账号测试
    account = accounts[0]
    print(f"📱 测试账号: {account['phone']}")
    
    # 创建钱包实例
    wallet = RNL(
        f"passToken={account['passToken']}; userId={account['userId']};"
    )
    
    # 查询用户信息
    print("\n📊 查询用户信息...")
    if not wallet.query_user_info():
        print("❌ 用户信息查询失败")
        return
    
    print(f"💎 总天数: {wallet.total_days}")
    print(f"📜 兑换历史: {'已兑换过' if wallet.has_exchanged_before else '从未兑换'}")
    
    # 检查兑换条件
    if wallet.total_days < 7:
        print(f"⚠️ 天数不足，无法兑换（需要≥7天，当前{wallet.total_days}天）")
        return
    
    if wallet.has_exchanged_before:
        print("⚠️ 该账号已兑换过会员")
        # 为了测试，我们继续尝试兑换
        print("🧪 测试模式：继续尝试兑换...")
    
    # 执行兑换
    test_phone = "***********"  # 指定的测试手机号
    print(f"\n🎬 开始兑换腾讯会员到手机号: {test_phone}")
    
    success = wallet.exchange_member(test_phone)
    
    print(f"\n📋 兑换结果: {'✅ 成功' if success else '❌ 失败'}")
    
    return success

if __name__ == "__main__":
    test_original_exchange()
