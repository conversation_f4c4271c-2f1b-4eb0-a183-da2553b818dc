<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小米钱包会员兑换系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }
        
        .stock-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stock-item {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .stock-available {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .stock-unavailable {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;  /* 减少间距从20px到15px */
            margin-top: 20px;
        }

        .account-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;  /* 减少内边距从20px到15px */
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s;
            cursor: pointer;  /* 添加手型光标 */
        }

        .account-card:hover {
            transform: translateY(-3px);  /* 减少悬停效果从-5px到-3px */
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);  /* 调整阴影 */
        }

        .account-card.selected {
            border: 3px solid #4CAF50 !important;
            background: #f8fff8;
        }

        .account-card.selected .card-status {
            background: #4CAF50;
            color: white;
        }
        
        .account-info {
            margin-bottom: 15px;
        }

        .card-status {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }

        /* 兑换记录操作按钮 */
        .record-actions {
            display: inline-flex;
            gap: 5px;
            margin-left: 10px;
        }

        .record-btn {
            padding: 4px 8px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .record-btn.generate {
            background: #4CAF50;
            color: white;
        }

        .record-btn.generate:hover {
            background: #45a049;
        }

        .record-btn.save {
            background: #2196F3;
            color: white;
        }

        .record-btn.save:hover {
            background: #1976D2;
        }

        /* 隐藏的图片生成容器 */
        .image-generator {
            position: fixed;
            top: -9999px;
            left: -9999px;
            background: white;
        }

        /* 兑换卡片样式 */
        .exchange-card {
            width: 400px;
            padding: 20px;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            font-family: 'Microsoft YaHei', sans-serif;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .exchange-card .field {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .exchange-card .field-icon {
            font-size: 18px;
            margin-right: 8px;
            width: 25px;
        }

        .exchange-card .field-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .exchange-card .field-value {
            color: #555;
            font-size: 16px;
        }

        .exchange-card .status-success {
            color: #4CAF50;
            font-weight: bold;
        }

        .exchange-card .status-failed {
            color: #f44336;
            font-weight: bold;
        }

        /* 跳过的账号样式 */
        .skipped-account {
            opacity: 0.7;
            border: 2px dashed #ff9800 !important;
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%) !important;
        }

        .skipped-account .account-status {
            background: #fff3e0 !important;
            color: #ff9800 !important;
            border: 1px solid #ffcc02;
        }
        
        .account-info div {
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .account-info .label {
            font-weight: bold;
            color: #666;
        }
        
        .account-info .value {
            color: #333;
        }
        
        .days-badge {
            background: #4CAF50;
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .days-badge.loading {
            background: #ff9800;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .message {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .exchange-history {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .exchange-record {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .exchange-record:last-child {
            margin-bottom: 0;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .accounts-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎁 小米钱包会员兑换系统</h1>
            <p>智能兑换 · 实时库存 · 批量管理</p>
        </div>
        
        <div class="main-content">
            <!-- 库存查询区域 -->
            <div class="section">
                <h2>📦 实时库存状态</h2>
                <button class="btn btn-secondary" onclick="checkStock()">🔄 刷新库存</button>
                <div id="stockStatus" class="stock-grid">
                    <div class="loading">正在加载库存信息...</div>
                </div>
            </div>
            
            <!-- 兑换区域 -->
            <div class="section">
                <h2>🎯 会员兑换</h2>
                <div class="form-group">
                    <label for="phoneInput">手机号码:</label>
                    <input type="tel" id="phoneInput" placeholder="请输入11位手机号" maxlength="11">
                </div>
                <div class="form-group">
                    <label for="activityType">活动类型:</label>
                    <select id="activityType" onchange="updateExchangeTypes()">
                        <option value="video">视频会员</option>
                        <option value="music">音乐会员</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="exchangeType">会员类型:</label>
                    <select id="exchangeType">
                        <option value="iqiyi">爱奇艺黄金会员月卡</option>
                        <option value="tencent">腾讯视频VIP月卡</option>
                        <option value="youku">优酷VIP会员月卡</option>
                        <option value="mango">芒果TV会员月卡</option>
                    </select>
                </div>
                <div id="exchangeMessage"></div>
            </div>
            
            <!-- 账号管理区域 -->
            <div class="section">
                <h2>👥 可用账号管理</h2>
                <div style="margin-bottom: 15px;">
                    <button class="btn btn-secondary" onclick="loadAccountsPage(1)">🔄 刷新账号</button>
                    <button class="btn" onclick="forceRefreshDays()">🔄 强制刷新天数</button>
                    <span style="margin-left: 20px; color: #666;">每页显示5个账号（缓存永久有效，手动刷新更新）</span>
                </div>

                <!-- 分页控件 -->
                <div id="paginationContainer" style="margin-bottom: 15px; text-align: center;">
                    <!-- 分页按钮将在这里动态生成 -->
                </div>

                <div id="accountsContainer">
                    <div class="loading">正在加载账号信息...</div>
                </div>
            </div>
            
            <!-- 兑换记录 -->
            <div class="section">
                <h2>📋 兑换记录</h2>
                <button class="btn btn-danger" onclick="clearHistory()">🗑️ 清空记录</button>
                <div id="exchangeHistory" class="exchange-history">
                    <p>暂无兑换记录</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let accounts = [];
        let currentPage = 1;
        let totalPages = 1;

        // 前端用户ID掩码函数
        function mask_user_id(userId) {
            if (!userId || userId.length < 6) return userId;
            return userId.substring(0, 3) + '******' + userId.substring(userId.length - 3);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkStock();
            loadAccountsPage(1);
            loadExchangeHistory();
        });
        
        // 检查库存状态
        async function checkStock() {
            try {
                const response = await fetch('/api/stock');
                const stockData = await response.json();
                
                const stockContainer = document.getElementById('stockStatus');
                stockContainer.innerHTML = '';
                
                const typeNames = {
                    'iqiyi': '爱奇艺黄金会员月卡',
                    'tencent': '腾讯视频VIP月卡',
                    'youku': '优酷VIP会员月卡',
                    'mango': '芒果TV会员月卡',
                    'bilibili': '哔哩哔哩会员月卡'
                };
                
                for (const [type, info] of Object.entries(stockData)) {
                    const stockItem = document.createElement('div');
                    stockItem.className = `stock-item ${info.has_stock ? 'stock-available' : 'stock-unavailable'}`;
                    stockItem.innerHTML = `
                        <div>${typeNames[type] || info.name}</div>
                        <div>${info.has_stock ? '🟢 有货' : '🔴 无货'}</div>
                    `;
                    stockContainer.appendChild(stockItem);
                }
            } catch (error) {
                document.getElementById('stockStatus').innerHTML = '<div class="message error">库存查询失败</div>';
            }
        }
        
        // 加载指定页的账号信息
        async function loadAccountsPage(page = 1, forceRefresh = false) {
            try {
                document.getElementById('accountsContainer').innerHTML = '<div class="loading">正在加载账号信息...</div>';

                // 获取当前选择的活动类型
                const activityType = document.getElementById('activityType').value;
                const forceParam = forceRefresh ? '&force_refresh=true' : '';
                const response = await fetch(`/api/accounts?page=${page}&per_page=5&activity_type=${activityType}${forceParam}`);
                const data = await response.json();

                if (data.accounts) {
                    accounts = data.accounts;
                    currentPage = data.pagination.page;
                    totalPages = data.pagination.total_pages;

                    // 显示统计信息
                    const statsDiv = document.createElement('div');
                    statsDiv.className = 'message';
                    statsDiv.innerHTML = `📊 可用账号总数: ${data.pagination.total}，第 ${currentPage}/${totalPages} 页`;
                    document.getElementById('accountsContainer').innerHTML = '';
                    document.getElementById('accountsContainer').appendChild(statsDiv);

                    // 更新分页控件
                    updatePagination(data.pagination);

                    // 账号信息已在后端过滤和加载，直接显示
                    displayAccounts();
                } else {
                    document.getElementById('accountsContainer').innerHTML = '<div class="message error">加载失败</div>';
                }

            } catch (error) {
                document.getElementById('accountsContainer').innerHTML = '<div class="message error">账号加载失败</div>';
            }
        }


        
        // 显示账号信息
        function displayAccounts() {
            const container = document.getElementById('accountsContainer');

            if (accounts.length === 0) {
                container.innerHTML = '<div class="message">暂无符合条件的账号（天数≥31天）</div>';
                return;
            }

            container.innerHTML = `
                <div class="accounts-grid" id="accountsGrid">
                    ${accounts.map((account, index) => {
                        // 判断是否为当月已兑换账号
                        const isExchangedThisMonth = account.status === 'exchanged_this_month';
                        const cardClass = isExchangedThisMonth ? 'account-card skipped-account' : 'account-card';
                        const statusText = isExchangedThisMonth ? account.skipReason : (account.hasExchanged ? '已兑换' : '未兑换');

                        return `
                        <div class="${cardClass}" id="account-${account.userId}" data-index="${index}"
                             onclick="selectAccount('${account.userId}', '${account.phone}')" data-user-id="${account.userId}">
                            <div class="account-info">
                                <div><span class="label">用户ID:</span> <span class="value">${account.maskedUserId || mask_user_id(account.userId)}</span></div>
                                <div><span class="label">手机号:</span> <span class="value">${account.phone}</span></div>
                                <div><span class="label">总天数:</span> <span class="days-badge">${account.totalDays ? account.totalDays + '天' : '31+天'}</span></div>
                                <div><span class="label">兑换状态:</span> <span class="value account-status">${statusText}</span></div>
                                <div><span class="label">Token:</span> <span class="value">${account.passToken}</span></div>
                            </div>
                            <div class="card-status">
                                ${isExchangedThisMonth ? '⏭️ 当月已兑换' : '👆 点击选择此账号'}
                                ${account.status === 'cached' ? '<span style="font-size: 12px; color: #666; margin-left: 10px;">📋 缓存</span>' : ''}
                                ${account.status === 'fresh' ? '<span style="font-size: 12px; color: #4caf50; margin-left: 10px;">🔄 实时</span>' : ''}
                            </div>
                        </div>
                        `;
                    }).join('')}
                </div>
            `;
        }

        // 更新单个账号卡片
        function updateAccountCard(index, details) {
            const daysElement = document.getElementById(`days-${details.userId}`);
            const statusElement = document.getElementById(`status-${details.userId}`);
            const cardStatusElement = document.getElementById(`card-status-${details.userId}`);
            const cardElement = document.getElementById(`account-${details.userId}`);

            if (daysElement) daysElement.textContent = details.totalDays + '天';
            if (statusElement) statusElement.textContent = details.hasExchanged ? '已兑换' : '未兑换';
            if (cardStatusElement) {
                cardStatusElement.textContent = '👆 点击选择此账号';
            }
            if (cardElement) {
                cardElement.style.opacity = '1';
                cardElement.style.cursor = 'pointer';
            }
        }

        // 移除账号卡片
        function removeAccountCard(userId) {
            const cardElement = document.getElementById(`account-${userId}`);
            if (cardElement) {
                cardElement.remove();
            }
        }
        
        // 选择账号进行兑换
        function selectAccount(userId, defaultPhone) {
            // 检查账号是否在查询中
            const cardElement = document.getElementById(`account-${userId}`);
            if (cardElement && cardElement.style.opacity === '0.6') {
                return; // 如果在查询中，不执行选择操作
            }

            // 不自动填充手机号，让用户自己输入
            // const phoneInput = document.getElementById('phoneInput');
            // if (!phoneInput.value) {
            //     phoneInput.value = defaultPhone;
            // }

            // 高亮选中的账号
            document.querySelectorAll('.account-card').forEach(card => {
                card.classList.remove('selected');
            });

            cardElement.classList.add('selected');

            // 存储选中的账号ID
            window.selectedUserId = userId;

            // 显示选择成功消息（脱敏显示手机号）
            const maskedPhone = defaultPhone.substring(0,3) + '****' + defaultPhone.substring(7);
            showMessage(`已选择账号 ${maskedPhone}`, 'success');
        }
        
        // 执行兑换
        async function performExchange() {
            const phone = document.getElementById('phoneInput').value;
            const exchangeType = document.getElementById('exchangeType').value;
            const activityType = document.getElementById('activityType').value;
            const userId = window.selectedUserId;
            
            if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
                showMessage('请输入正确的手机号', 'error');
                return;
            }
            
            if (!userId) {
                showMessage('请先选择一个账号', 'error');
                return;
            }
            
            try {
                showMessage('正在兑换中，请稍候...', 'success');
                
                const response = await fetch('/api/exchange', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone: phone,
                        userId: userId,
                        exchangeType: exchangeType,
                        activityType: activityType
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 显示成功消息和详细信息
                    let successHtml = `
                        <div class="message success">
                            🎉 兑换成功！${result.message}
                        </div>
                        <div class="exchange-details" style="background: #e8f5e9; border: 2px solid #4CAF50; border-radius: 8px; padding: 20px; margin-top: 15px;">
                            <h4 style="color: #2e7d32; margin-bottom: 15px;">📋 兑换详情</h4>
                            <div style="margin-bottom: 10px;"><strong>📱 充值手机号:</strong> ${result.phone}</div>
                            <div style="margin-bottom: 10px;"><strong>🎬 会员类型:</strong> ${getTypeName(result.exchangeType)}</div>
                            <div style="margin-bottom: 10px;"><strong>🕐 兑换时间:</strong> ${result.time}</div>
                            <div style="margin-bottom: 10px;"><strong>👤 使用账号:</strong> ${result.accountInfo.maskedUserId}</div>
                            <div style="margin-bottom: 15px;"><strong>🔑 账号Token:</strong> ${result.accountInfo.passToken}</div>

                            <h4 style="color: #2e7d32; margin-bottom: 10px;">🔧 后端返回信息</h4>
                            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; font-family: 'Courier New', monospace; font-size: 14px; white-space: pre-wrap; overflow-x: auto;">
${JSON.stringify(result.rawResponse, null, 2)}
                            </div>
                        </div>
                    `;

                    document.getElementById('exchangeMessage').innerHTML = successHtml;

                    // 重新加载兑换记录和账号
                    loadExchangeHistory();

                    // 重新加载当前页账号（移除已兑换的账号）
                    setTimeout(() => {
                        loadAccountsPage(currentPage);
                    }, 2000);

                } else {
                    // 显示失败消息和详细信息
                    let errorHtml = `
                        <div class="message error">
                            ❌ 兑换失败：${result.message}
                        </div>
                    `;

                    if (result.rawResponse) {
                        errorHtml += `
                            <div style="background: #f8d7da; border: 2px solid #f5c6cb; border-radius: 8px; padding: 20px; margin-top: 15px;">
                                <h4 style="color: #721c24; margin-bottom: 10px;">🔧 后端返回信息</h4>
                                <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; font-family: 'Courier New', monospace; font-size: 14px; white-space: pre-wrap; overflow-x: auto;">
${JSON.stringify(result.rawResponse, null, 2)}
                                </div>
                            </div>
                        `;
                    }

                    document.getElementById('exchangeMessage').innerHTML = errorHtml;
                }
                
            } catch (error) {
                showMessage('兑换请求失败，请重试', 'error');
            }
        }
        
        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('exchangeMessage');
            messageDiv.innerHTML = `<div class="message ${type}">${message}</div>`;

            if (type === 'success') {
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 10000);  // 延长显示时间到10秒
            }
        }
        
        // 加载兑换历史
        async function loadExchangeHistory() {
            try {
                const response = await fetch('/api/exchange-records');
                const records = await response.json();
                displayExchangeHistory(records);
            } catch (error) {
                console.error('加载兑换记录失败:', error);
                document.getElementById('exchangeHistory').innerHTML = '<p>加载兑换记录失败</p>';
            }
        }

        // 显示兑换历史
        function displayExchangeHistory(records) {
            exchangeRecords = records; // 保存到全局变量
            const historyContainer = document.getElementById('exchangeHistory');

            if (!records || records.length === 0) {
                historyContainer.innerHTML = '<p>暂无兑换记录</p>';
                return;
            }

            // 根据活动类型显示不同的会员名称
            function getTypeDisplayName(record) {
                const activityType = record.activityType || 'video';
                if (activityType === 'music') {
                    return '音乐VIP';
                } else {
                    const videoNames = {
                        'iqiyi': '爱奇艺',
                        'tencent': '腾讯视频',
                        'youku': '优酷',
                        'mango': '芒果TV'
                    };
                    return videoNames[record.exchangeType] || record.exchangeType;
                }
            }

            historyContainer.innerHTML = records.map((record, index) => `
                <div class="exchange-record">
                    <strong>📱 ${record.phone}</strong> |
                    ${record.activityType === 'music' ? '🎵' : '🎬'} ${getTypeDisplayName(record)} |
                    🕐 ${record.time} |
                    ${record.success ? '✅ 成功' : '❌ 失败'}
                    ${record.message ? ` (${record.message})` : ''}
                    <div class="record-actions">
                        <button class="record-btn generate" onclick="generateExchangeImage(${index})" title="生成图片">
                            🖼️ 生成图片
                        </button>
                        <button class="record-btn save" onclick="saveExchangeImage(${index})" title="保存到本地">
                            💾 保存
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 清空兑换记录
        async function clearHistory() {
            if (confirm('确定要清空所有兑换记录吗？')) {
                try {
                    const response = await fetch('/api/exchange-records', {
                        method: 'DELETE'
                    });
                    const result = await response.json();

                    if (result.success) {
                        displayExchangeHistory([]);
                        // 重新加载账号（因为清空记录后可能有更多可用账号）
                        loadAccountsPage(1);
                    } else {
                        alert('清空失败: ' + result.message);
                    }
                } catch (error) {
                    alert('清空失败: ' + error.message);
                }
            }
        }
        
        // 更新分页控件
        function updatePagination(pagination) {
            const container = document.getElementById('paginationContainer');
            let html = '';

            // 上一页按钮
            if (pagination.has_prev) {
                html += `<button class="btn btn-secondary" onclick="loadAccountsPage(${pagination.page - 1})">⬅️ 上一页</button> `;
            }

            // 页码按钮
            const startPage = Math.max(1, pagination.page - 2);
            const endPage = Math.min(pagination.total_pages, pagination.page + 2);

            for (let i = startPage; i <= endPage; i++) {
                if (i === pagination.page) {
                    html += `<button class="btn" style="background: #ff6b6b;">${i}</button> `;
                } else {
                    html += `<button class="btn btn-secondary" onclick="loadAccountsPage(${i})">${i}</button> `;
                }
            }

            // 下一页按钮
            if (pagination.has_next) {
                html += `<button class="btn btn-secondary" onclick="loadAccountsPage(${pagination.page + 1})">下一页 ➡️</button>`;
            }

            container.innerHTML = html;
        }

        // 强制刷新所有账号天数
        async function forceRefreshDays() {
            const activityType = document.getElementById('activityType').value;
            const button = event.target;
            const originalText = button.textContent;

            try {
                button.textContent = '🔄 刷新中...';
                button.disabled = true;

                const response = await fetch('/api/refresh-days', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        activityType: activityType
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert(`✅ ${result.message}`);
                    // 刷新完成后重新加载账号列表
                    loadAccountsPage(1, true); // 传递force_refresh参数
                } else {
                    alert(`❌ ${result.message}`);
                }

            } catch (error) {
                console.error('强制刷新失败:', error);
                alert('❌ 刷新失败，请检查网络连接');
            } finally {
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        // 获取会员类型中文名称
        function getTypeName(type) {
            const typeNames = {
                'iqiyi': '爱奇艺黄金会员月卡',
                'tencent': '腾讯视频VIP月卡',
                'youku': '优酷VIP会员月卡',
                'mango': '芒果TV会员月卡'
            };
            return typeNames[type] || type;
        }
        
        // 绑定兑换按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            // 创建兑换按钮
            const exchangeSection = document.querySelector('.section:nth-child(2)');
            const exchangeButton = document.createElement('button');
            exchangeButton.className = 'btn';
            exchangeButton.textContent = '🎁 立即兑换';
            exchangeButton.onclick = performExchange;
            exchangeSection.appendChild(exchangeButton);
        });

        // 全局变量存储兑换记录
        let exchangeRecords = [];

        // 更新兑换类型选项
        function updateExchangeTypes() {
            const activityType = document.getElementById('activityType').value;
            const exchangeTypeSelect = document.getElementById('exchangeType');

            if (activityType === 'music') {
                exchangeTypeSelect.innerHTML = `
                    <option value="music">音乐VIP月卡</option>
                `;
            } else {
                exchangeTypeSelect.innerHTML = `
                    <option value="iqiyi">爱奇艺黄金会员月卡</option>
                    <option value="tencent">腾讯视频VIP月卡</option>
                    <option value="youku">优酷VIP会员月卡</option>
                    <option value="mango">芒果TV会员月卡</option>
                `;
            }

            // 切换活动类型时重新加载账号
            loadAccountsPage(1);
        }

        // 复制图片到剪贴板的通用函数
        async function copyImageToClipboard(canvas, record) {
            try {
                // 方案1: 使用现代剪贴板API (需要HTTPS)
                if (navigator.clipboard && navigator.clipboard.write && window.ClipboardItem) {
                    canvas.toBlob(async (blob) => {
                        try {
                            await navigator.clipboard.write([
                                new ClipboardItem({ 'image/png': blob })
                            ]);
                            showMessage('图片已复制到剪贴板！', 'success');
                        } catch (err) {
                            console.error('剪贴板API失败:', err);
                            const link = document.createElement('a');
                            link.download = `兑换记录_${record.time.replace(/[:\s-]/g, '')}.png`;
                            link.href = canvas.toDataURL('image/png');
                            link.click();
                            showMessage('复制失败，图片已自动下载！', 'warning');
                        }
                    });
                    return;
                }

                // 方案2: 尝试复制dataURL到剪贴板 (兼容性更好)
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    const dataURL = canvas.toDataURL('image/png');
                    await navigator.clipboard.writeText(dataURL);
                    showMessage('图片数据已复制到剪贴板！', 'success');
                    return;
                }

                // 方案3: 降级到下载
                fallbackDownload(canvas, record);

            } catch (error) {
                console.error('复制失败:', error);
                fallbackDownload(canvas, record);
            }
        }

        // 降级方案：直接下载图片
        function fallbackDownload(canvas, record) {
            const link = document.createElement('a');
            link.download = `兑换记录_${record.time.replace(/[:\s-]/g, '')}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
            showMessage('浏览器不支持剪贴板，图片已自动下载！', 'warning');
        }

        // 生成兑换图片并复制到剪贴板
        async function generateExchangeImage(index) {
            const record = exchangeRecords[index];
            if (!record) return;

            // 根据活动类型获取完整的会员名称
            const activityType = record.activityType || 'video';
            let typeName;
            if (activityType === 'music') {
                typeName = '音乐VIP月卡';
            } else {
                const videoNames = {
                    'iqiyi': '爱奇艺黄金会员月卡',
                    'tencent': '腾讯视频VIP月卡',
                    'youku': '优酷VIP会员月卡',
                    'mango': '芒果TV会员月卡'
                };
                typeName = videoNames[record.exchangeType] || record.exchangeType;
            }

            // 创建图片生成容器
            const container = document.createElement('div');
            container.className = 'image-generator';
            container.innerHTML = `
                <div class="exchange-card" id="exchange-card-${index}">
                    <div class="field">
                        <span class="field-icon">📱</span>
                        <div>
                            <div class="field-label">充值手机号</div>
                            <div class="field-value">${record.phone}</div>
                        </div>
                    </div>
                    <div class="field">
                        <span class="field-icon">🎬</span>
                        <div>
                            <div class="field-label">会员类型</div>
                            <div class="field-value">${typeName}</div>
                        </div>
                    </div>
                    <div class="field">
                        <span class="field-icon">🕐</span>
                        <div>
                            <div class="field-label">兑换时间</div>
                            <div class="field-value">${record.time}</div>
                        </div>
                    </div>
                    <div class="field">
                        <span class="field-icon">${record.success ? '✅' : '❌'}</span>
                        <div>
                            <div class="field-label">兑换状态</div>
                            <div class="field-value ${record.success ? 'status-success' : 'status-failed'}">
                                ${record.success ? '成功' : '失败'}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(container);

            try {
                // 使用html2canvas生成图片
                const canvas = await html2canvas(document.getElementById(`exchange-card-${index}`), {
                    backgroundColor: '#ffffff',
                    scale: 2,
                    useCORS: true
                });

                // 尝试复制到剪贴板的多种方案
                await copyImageToClipboard(canvas, record);

            } catch (error) {
                console.error('生成图片失败:', error);
                showMessage('生成图片失败', 'error');
            } finally {
                document.body.removeChild(container);
            }
        }

        // 保存兑换图片到本地
        async function saveExchangeImage(index) {
            const record = exchangeRecords[index];
            if (!record) return;

            // 根据活动类型获取完整的会员名称
            const activityType = record.activityType || 'video';
            let typeName;
            if (activityType === 'music') {
                typeName = '音乐VIP月卡';
            } else {
                const videoNames = {
                    'iqiyi': '爱奇艺黄金会员月卡',
                    'tencent': '腾讯视频VIP月卡',
                    'youku': '优酷VIP会员月卡',
                    'mango': '芒果TV会员月卡'
                };
                typeName = videoNames[record.exchangeType] || record.exchangeType;
            }

            // 创建图片生成容器
            const container = document.createElement('div');
            container.className = 'image-generator';
            container.innerHTML = `
                <div class="exchange-card" id="exchange-card-save-${index}">
                    <div class="field">
                        <span class="field-icon">📱</span>
                        <div>
                            <div class="field-label">充值手机号</div>
                            <div class="field-value">${record.phone}</div>
                        </div>
                    </div>
                    <div class="field">
                        <span class="field-icon">🎬</span>
                        <div>
                            <div class="field-label">会员类型</div>
                            <div class="field-value">${typeName}</div>
                        </div>
                    </div>
                    <div class="field">
                        <span class="field-icon">🕐</span>
                        <div>
                            <div class="field-label">兑换时间</div>
                            <div class="field-value">${record.time}</div>
                        </div>
                    </div>
                    <div class="field">
                        <span class="field-icon">${record.success ? '✅' : '❌'}</span>
                        <div>
                            <div class="field-label">兑换状态</div>
                            <div class="field-value ${record.success ? 'status-success' : 'status-failed'}">
                                ${record.success ? '成功' : '失败'}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(container);

            try {
                // 使用html2canvas生成图片
                const canvas = await html2canvas(document.getElementById(`exchange-card-save-${index}`), {
                    backgroundColor: '#ffffff',
                    scale: 2,
                    useCORS: true
                });

                // 创建下载链接
                const link = document.createElement('a');
                link.download = `兑换记录_${record.time.replace(/[:\s-]/g, '')}.png`;
                link.href = canvas.toDataURL();
                link.click();

                showMessage('图片已保存到本地！', 'success');

            } catch (error) {
                console.error('保存图片失败:', error);
                showMessage('保存图片失败', 'error');
            } finally {
                document.body.removeChild(container);
            }
        }
    </script>

    <!-- 引入html2canvas库 -->
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
</body>
</html>
