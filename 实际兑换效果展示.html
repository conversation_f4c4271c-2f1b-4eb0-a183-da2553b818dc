<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实际兑换效果展示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            color: #555;
            margin-bottom: 15px;
        }
        
        .message {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            animation: slideIn 0.5s ease-out;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .exchange-details {
            background: white;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: bold;
            color: #666;
        }
        
        .detail-value {
            color: #333;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎁 实际兑换效果展示</h1>
        
        <!-- 1. 兑换过程中的提示 -->
        <div class="demo-section">
            <div class="demo-title">1. 兑换过程中的提示</div>
            <div class="message loading" id="loadingMsg">
                🔄 正在兑换中，请稍候...
            </div>
        </div>
        
        <!-- 2. 兑换成功的显示 -->
        <div class="demo-section">
            <div class="demo-title">2. 兑换成功后的显示</div>
            <div class="message success" id="successMsg" style="display: none;">
                🎉 兑换成功！爱奇艺黄金会员月卡已充值完成
            </div>
            
            <div class="exchange-details" id="successDetails" style="display: none;">
                <div class="detail-row">
                    <span class="detail-label">📱 充值手机号:</span>
                    <span class="detail-value">***********</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">🎬 会员类型:</span>
                    <span class="detail-value">爱奇艺黄金会员月卡</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">🕐 兑换时间:</span>
                    <span class="detail-value">2025-07-03 02:35:42</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">👤 使用账号:</span>
                    <span class="detail-value">305******661</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">🔑 账号Token:</span>
                    <span class="detail-value">V1:DXmurwq2/R1BHTELu...</span>
                </div>
            </div>

            <!-- 后端返回信息 -->
            <div id="backendResponse" style="display: none; margin-top: 15px;">
                <h4 style="color: #2e7d32; margin-bottom: 10px;">🔧 后端返回信息</h4>
                <div class="json-display">
{
  "code": 0,
  "message": "兑换成功",
  "value": {
    "exchangeId": "EX20250703023542001",
    "phone": "***********",
    "exchangeType": "iqiyi",
    "status": "SUCCESS",
    "timestamp": 1720000542000
  }
}
                </div>
            </div>
        </div>
        
        <!-- 3. 兑换失败的显示 -->
        <div class="demo-section">
            <div class="demo-title">3. 兑换失败时的显示</div>
            <div class="message error" id="errorMsg" style="display: none;">
                ❌ 兑换失败：缺货补货中，明天再试
            </div>
        </div>
        
        <!-- 4. 后端返回的JSON数据 -->
        <div class="demo-section">
            <div class="demo-title">4. 后端返回的JSON数据示例</div>
            <div class="json-display" id="jsonDisplay">
{
  "success": true,
  "message": "兑换成功",
  "phone": "***********",
  "exchangeType": "iqiyi",
  "time": "2025-07-03 02:35:42"
}
            </div>
        </div>
        
        <!-- 5. 兑换记录更新 -->
        <div class="demo-section">
            <div class="demo-title">5. 兑换记录会自动更新</div>
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px;">
                <div style="font-weight: bold; margin-bottom: 10px;">📋 最新兑换记录</div>
                <div style="background: white; border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin-bottom: 5px;">
                    <strong>📱 ***********</strong> | 🎬 爱奇艺 | 🕐 2025-07-03 02:35:42 | ✅ 成功
                </div>
                <div style="background: white; border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin-bottom: 5px;">
                    <strong>📱 19202514279</strong> | 🎬 腾讯视频 | 🕐 2025-07-03 01:28:15 | ✅ 成功
                </div>
                <div style="background: white; border: 1px solid #ddd; border-radius: 5px; padding: 10px;">
                    <strong>📱 19212425803</strong> | 🎬 优酷 | 🕐 2025-07-02 23:45:33 | ✅ 成功
                </div>
            </div>
        </div>
        
        <!-- 演示按钮 -->
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="simulateExchange()">🎬 模拟兑换过程</button>
            <button class="btn" onclick="showError()">❌ 模拟兑换失败</button>
            <button class="btn" onclick="reset()">🔄 重置演示</button>
        </div>
    </div>

    <script>
        function simulateExchange() {
            // 重置状态
            reset();
            
            // 显示加载状态
            document.getElementById('loadingMsg').style.display = 'block';
            
            // 2秒后显示成功
            setTimeout(() => {
                document.getElementById('loadingMsg').style.display = 'none';
                document.getElementById('successMsg').style.display = 'block';
                document.getElementById('successDetails').style.display = 'block';
                document.getElementById('backendResponse').style.display = 'block';

                // 更新JSON显示为成功
                document.getElementById('jsonDisplay').textContent = `{
  "success": true,
  "message": "兑换成功",
  "phone": "***********",
  "exchangeType": "iqiyi",
  "time": "${new Date().toLocaleString('zh-CN')}",
  "rawResponse": {
    "code": 0,
    "message": "兑换成功",
    "value": {
      "exchangeId": "EX${new Date().getTime()}",
      "phone": "***********",
      "exchangeType": "iqiyi",
      "status": "SUCCESS",
      "timestamp": ${new Date().getTime()}
    }
  },
  "accountInfo": {
    "maskedUserId": "305******661",
    "passToken": "V1:DXmurwq2/R1BHTELu..."
  }
}`;
            }, 2000);
        }
        
        function showError() {
            // 重置状态
            reset();
            
            // 显示加载状态
            document.getElementById('loadingMsg').style.display = 'block';
            
            // 2秒后显示失败
            setTimeout(() => {
                document.getElementById('loadingMsg').style.display = 'none';
                document.getElementById('errorMsg').style.display = 'block';
                
                // 更新JSON显示为失败
                document.getElementById('jsonDisplay').textContent = `{
  "success": false,
  "message": "兑换失败: 缺货补货中",
  "phone": "***********",
  "exchangeType": "iqiyi",
  "time": "${new Date().toLocaleString('zh-CN')}"
}`;
            }, 2000);
        }
        
        function reset() {
            document.getElementById('loadingMsg').style.display = 'none';
            document.getElementById('successMsg').style.display = 'none';
            document.getElementById('successDetails').style.display = 'none';
            document.getElementById('errorMsg').style.display = 'none';
            
            // 重置JSON显示
            document.getElementById('jsonDisplay').textContent = `{
  "success": true,
  "message": "兑换成功",
  "phone": "***********",
  "exchangeType": "iqiyi",
  "time": "2025-07-03 02:35:42"
}`;
        }
        
        // 页面加载时显示一次成功效果
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                simulateExchange();
            }, 1000);
        });
    </script>
</body>
</html>
