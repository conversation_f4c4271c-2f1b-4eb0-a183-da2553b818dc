<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试图片生成</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .exchange-card {
            width: 400px;
            padding: 20px;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            font-family: 'Microsoft YaHei', sans-serif;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px auto;
        }
        
        .exchange-card .field {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .exchange-card .field-icon {
            font-size: 18px;
            margin-right: 8px;
            width: 25px;
        }
        
        .exchange-card .field-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .exchange-card .field-value {
            color: #555;
            font-size: 16px;
        }
        
        .exchange-card .status-success {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn.save {
            background: #2196F3;
        }
        
        .btn.save:hover {
            background: #1976D2;
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 图片生成测试</h1>
        
        <div class="exchange-card" id="test-card">
            <div class="field">
                <span class="field-icon">📱</span>
                <div>
                    <div class="field-label">充值手机号</div>
                    <div class="field-value">19202517653</div>
                </div>
            </div>
            <div class="field">
                <span class="field-icon">🎬</span>
                <div>
                    <div class="field-label">会员类型</div>
                    <div class="field-value">腾讯视频VIP月卡</div>
                </div>
            </div>
            <div class="field">
                <span class="field-icon">🕐</span>
                <div>
                    <div class="field-label">兑换时间</div>
                    <div class="field-value">2025-07-03 04:08:43</div>
                </div>
            </div>
            <div class="field">
                <span class="field-icon">✅</span>
                <div>
                    <div class="field-label">兑换状态</div>
                    <div class="field-value status-success">成功</div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center;">
            <button class="btn" onclick="generateImage()">🖼️ 生成并复制图片</button>
            <button class="btn save" onclick="saveImage()">💾 保存图片</button>
        </div>
        
        <div id="message"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script>
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${text}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }
        
        async function generateImage() {
            try {
                const canvas = await html2canvas(document.getElementById('test-card'), {
                    backgroundColor: '#ffffff',
                    scale: 2,
                    useCORS: true
                });
                
                // 尝试复制到剪贴板
                try {
                    if (navigator.clipboard && navigator.clipboard.write && window.ClipboardItem) {
                        canvas.toBlob(async (blob) => {
                            try {
                                await navigator.clipboard.write([
                                    new ClipboardItem({ 'image/png': blob })
                                ]);
                                showMessage('图片已复制到剪贴板！', 'success');
                            } catch (err) {
                                console.error('剪贴板API失败:', err);
                                fallbackDownload(canvas);
                            }
                        });
                    } else {
                        fallbackDownload(canvas);
                    }
                } catch (error) {
                    console.error('复制失败:', error);
                    fallbackDownload(canvas);
                }
                
            } catch (error) {
                console.error('生成图片失败:', error);
                showMessage('生成图片失败', 'error');
            }
        }
        
        async function saveImage() {
            try {
                const canvas = await html2canvas(document.getElementById('test-card'), {
                    backgroundColor: '#ffffff',
                    scale: 2,
                    useCORS: true
                });
                
                const link = document.createElement('a');
                link.download = '兑换记录_20250703040843.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
                
                showMessage('图片已保存到本地！', 'success');
                
            } catch (error) {
                console.error('保存图片失败:', error);
                showMessage('保存图片失败', 'error');
            }
        }
        
        function fallbackDownload(canvas) {
            const link = document.createElement('a');
            link.download = '兑换记录_20250703040843.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
            showMessage('浏览器不支持剪贴板，图片已自动下载！', 'warning');
        }
    </script>
</body>
</html>
