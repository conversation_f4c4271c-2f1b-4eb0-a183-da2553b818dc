[{"success": true, "message": "兑换成功！获得31天会员时长", "phone": "18428126544", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:30:56", "fullUserId": "3057663106", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057663106, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761632550229901312", "prizeGiveDesc1": "", "prizeId": 2055, "addressId": 0, "prizeGiveDesc2": "兑换芒果TV会员月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1264", "useTime": "2025-07-05 00:30:55", "activeEnd": "", "id": 1069889615, "sn": "", "prizeCode": "LSXD_PRIZE1264", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057663106, "prizeType": 26, "updateTime": "2025-07-05 00:30:55", "createTime": "2025-07-05 00:30:55", "status": 1}, "afterChangeValue": 876, "description": "兑换芒果TV会员月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:30:55", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:30:55", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "16678580895", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:30:31", "fullUserId": "3057718481", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057718481, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761632496274374657", "prizeGiveDesc1": "", "prizeId": 2055, "addressId": 0, "prizeGiveDesc2": "兑换芒果TV会员月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1264", "useTime": "2025-07-05 00:30:30", "activeEnd": "", "id": 1069888735, "sn": "", "prizeCode": "LSXD_PRIZE1264", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057718481, "prizeType": 26, "updateTime": "2025-07-05 00:30:30", "createTime": "2025-07-05 00:30:30", "status": 1}, "afterChangeValue": 942, "description": "兑换芒果TV会员月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:30:30", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:30:30", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "13787253439", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:30:00", "fullUserId": "3057632460", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057632460, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761632429970817024", "prizeGiveDesc1": "", "prizeId": 2055, "addressId": 0, "prizeGiveDesc2": "兑换芒果TV会员月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1264", "useTime": "2025-07-05 00:29:59", "activeEnd": "", "id": 1069887570, "sn": "", "prizeCode": "LSXD_PRIZE1264", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057632460, "prizeType": 26, "updateTime": "2025-07-05 00:29:59", "createTime": "2025-07-05 00:29:59", "status": 1}, "afterChangeValue": 934, "description": "兑换芒果TV会员月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:29:58", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:29:58", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "18393216875", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:29:20", "fullUserId": "3057696247", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057696247, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761632341991096320", "prizeGiveDesc1": "", "prizeId": 2055, "addressId": 0, "prizeGiveDesc2": "兑换芒果TV会员月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1264", "useTime": "2025-07-05 00:29:18", "activeEnd": "", "id": 1069886124, "sn": "", "prizeCode": "LSXD_PRIZE1264", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057696247, "prizeType": 26, "updateTime": "2025-07-05 00:29:18", "createTime": "2025-07-05 00:29:18", "status": 1}, "afterChangeValue": 934, "description": "兑换芒果TV会员月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:29:18", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:29:18", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "15859062762", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:22:41", "fullUserId": "3057753413", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057753413, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_indexwindow_TVcard", "couponId": "3761631487225495552", "prizeGiveDesc1": "", "prizeId": 2055, "addressId": 0, "prizeGiveDesc2": "兑换芒果TV会员月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"*******\",\"com.miui.video\":\"v2021102990(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.0.5301.2332\"}", "prizeBatchId": "LSXD_PRIZE1264", "useTime": "2025-07-05 00:22:40", "activeEnd": "", "id": 1069869388, "sn": "", "prizeCode": "LSXD_PRIZE1264", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057753413, "prizeType": 26, "updateTime": "2025-07-05 00:22:40", "createTime": "2025-07-05 00:22:40", "status": 1}, "afterChangeValue": 734, "description": "兑换芒果TV会员月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:22:40", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:22:40", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "13314371377", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:22:09", "fullUserId": "3057729805", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057729805, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761631418506018816", "prizeGiveDesc1": "", "prizeId": 2055, "addressId": 0, "prizeGiveDesc2": "兑换芒果TV会员月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1264", "useTime": "2025-07-05 00:22:08", "activeEnd": "", "id": 1069867855, "sn": "", "prizeCode": "LSXD_PRIZE1264", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057729805, "prizeType": 26, "updateTime": "2025-07-05 00:22:08", "createTime": "2025-07-05 00:22:08", "status": 1}, "afterChangeValue": 856, "description": "兑换芒果TV会员月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:22:07", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:22:07", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "15268355686", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:21:35", "fullUserId": "3057701931", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057701931, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761631345558683648", "prizeGiveDesc1": "", "prizeId": 2055, "addressId": 0, "prizeGiveDesc2": "兑换芒果TV会员月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1264", "useTime": "2025-07-05 00:21:34", "activeEnd": "", "id": 1069866389, "sn": "", "prizeCode": "LSXD_PRIZE1264", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057701931, "prizeType": 26, "updateTime": "2025-07-05 00:21:34", "createTime": "2025-07-05 00:21:34", "status": 1}, "afterChangeValue": 926, "description": "兑换芒果TV会员月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:21:34", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:21:34", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": false, "message": "积分不足，需要31.0天，当前只有9.3天", "phone": "15268355686", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:21:27", "fullUserId": "3057914643", "rawResponse": {"error": "积分不足", "required": 31.0, "current": 9.34}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "18673521583", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:20:52", "fullUserId": "3057914643", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057914643, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761631253149777920", "prizeGiveDesc1": "", "prizeId": 2055, "addressId": 0, "prizeGiveDesc2": "兑换芒果TV会员月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1264", "useTime": "2025-07-05 00:20:51", "activeEnd": "", "id": 1069864495, "sn": "", "prizeCode": "LSXD_PRIZE1264", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057914643, "prizeType": 26, "updateTime": "2025-07-05 00:20:51", "createTime": "2025-07-05 00:20:51", "status": 1}, "afterChangeValue": 934, "description": "兑换芒果TV会员月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:20:51", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:20:51", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "18924077356", "exchangeType": "<PERSON><PERSON><PERSON>", "activityType": "video", "time": "2025-07-05 00:20:02", "fullUserId": "3058104875", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3058104875, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761631145842704384", "prizeGiveDesc1": "", "prizeId": 2055, "addressId": 0, "prizeGiveDesc2": "兑换芒果TV会员月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1264", "useTime": "2025-07-05 00:20:01", "activeEnd": "", "id": 1069862195, "sn": "", "prizeCode": "LSXD_PRIZE1264", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3058104875, "prizeType": 26, "updateTime": "2025-07-05 00:20:01", "createTime": "2025-07-05 00:20:01", "status": 1}, "afterChangeValue": 872, "description": "兑换芒果TV会员月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:20:00", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:20:00", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "15822925406", "exchangeType": "tencent", "activityType": "video", "time": "2025-07-05 00:09:19", "fullUserId": "3057627983", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057627983, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761629764675174400", "prizeGiveDesc1": "", "prizeId": 2054, "addressId": 0, "prizeGiveDesc2": "兑换腾讯视频VIP月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1263", "useTime": "2025-07-05 00:09:18", "activeEnd": "", "id": 1069824200, "sn": "", "prizeCode": "LSXD_PRIZE1263", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057627983, "prizeType": 26, "updateTime": "2025-07-05 00:09:18", "createTime": "2025-07-05 00:09:18", "status": 1}, "afterChangeValue": 844, "description": "兑换腾讯视频VIP月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-05 00:09:18", "action": 2, "id": 0, "withdrawTime": "2025-07-05 00:09:18", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": false, "message": "积分不足，需要31.0天，当前只有9.3天", "phone": "15822925406", "exchangeType": "tencent", "activityType": "video", "time": "2025-07-05 00:09:10", "fullUserId": "3057907656", "rawResponse": {"error": "积分不足", "required": 31.0, "current": 9.26}}, {"success": false, "message": "积分不足，需要31.0天，当前只有6.3天", "phone": "15822925406", "exchangeType": "tencent", "activityType": "video", "time": "2025-07-05 00:09:02", "fullUserId": "3057699427", "rawResponse": {"error": "积分不足", "required": 31.0, "current": 6.32}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "18348185287", "exchangeType": "tencent", "time": "2025-07-03 04:19:06", "fullUserId": "3057907656", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057907656, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761290861690748928", "prizeGiveDesc1": "", "prizeId": 2054, "addressId": 0, "prizeGiveDesc2": "兑换腾讯视频VIP月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1263", "useTime": "2025-07-03 04:19:04", "activeEnd": "", "id": 1064663089, "sn": "", "prizeCode": "LSXD_PRIZE1263", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057907656, "prizeType": 26, "updateTime": "2025-07-03 04:19:04", "createTime": "2025-07-03 04:19:04", "status": 1}, "afterChangeValue": 816, "description": "兑换腾讯视频VIP月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-03 04:19:04", "action": 2, "id": 0, "withdrawTime": "2025-07-03 04:19:04", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": true, "message": "兑换成功！获得31天会员时长", "phone": "18348185287", "exchangeType": "tencent", "time": "2025-07-03 04:08:43", "fullUserId": "3057699427", "rawResponse": {"code": 0, "success": true, "error": "成功", "value": {"miId": 3057699427, "prizeInfo": {"prizeDesc": "31天会员时长", "channel": "mipay_FLbanner_TVcard", "couponId": "3761289526224355328", "prizeGiveDesc1": "", "prizeId": 2054, "addressId": 0, "prizeGiveDesc2": "兑换腾讯视频VIP月卡", "activityId": 126, "userExtra": "{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}", "prizeBatchId": "LSXD_PRIZE1263", "useTime": "2025-07-03 04:08:42", "activeEnd": "", "id": 1064661149, "sn": "", "prizeCode": "LSXD_PRIZE1263", "deviceType": 2, "app": "com.mipay.wallet", "prizeUrl": "", "buttonText": "", "amount": 0, "userTaskId": 0, "miId": 3057699427, "prizeType": 26, "updateTime": "2025-07-03 04:08:42", "createTime": "2025-07-03 04:08:42", "status": 1}, "afterChangeValue": 518, "description": "兑换腾讯视频VIP月卡", "prizeId": 0, "activityId": 126, "expireTime": "2027-01-01 00:00:00", "balance": 0, "createTime": "2025-07-03 04:08:42", "action": 2, "id": 0, "withdrawTime": "2025-07-03 04:08:42", "sn": "", "value": -3100, "firstFrom": "exchange", "taskId": 0, "status": 1}}}, {"success": false, "message": "积分不足，需要31.0天，当前只有0.0天", "phone": "18348185287", "exchangeType": "tencent", "time": "2025-07-03 04:00:49", "fullUserId": "3057699427", "rawResponse": {"error": "积分不足", "required": 31.0, "current": 0.0}}, {"success": false, "message": "积分不足，需要31.0天，当前只有0.0天", "phone": "18348185287", "exchangeType": "tencent", "time": "2025-07-03 03:50:18", "fullUserId": "3057699427", "rawResponse": {"error": "积分不足", "required": 31.0, "current": 0.0}}, {"success": false, "message": "积分不足，需要31.0天，当前只有0.0天", "phone": "18348185287", "exchangeType": "tencent", "time": "2025-07-03 03:49:26", "fullUserId": "3057699427", "rawResponse": {"error": "积分不足", "required": 31.0, "current": 0.0}}, {"success": false, "message": "积分不足，需要31.0天，当前只有0.0天", "phone": "18348185287", "exchangeType": "tencent", "time": "2025-07-03 03:43:42", "fullUserId": "3057699427", "rawResponse": {"error": "积分不足", "required": 31.0, "current": 0.0}}, {"success": false, "message": "兑换失败: 缺货补货中", "phone": "18348185287", "exchangeType": "tencent", "time": "2025-07-03 02:48:11", "fullUserId": "3057660661", "rawResponse": {"error": "无响应"}}, {"success": false, "message": "兑换失败: 缺货补货中", "phone": "18348185287", "exchangeType": "tencent", "time": "2025-07-03 02:43:05", "fullUserId": "3057660661"}, {"success": false, "message": "兑换失败: 缺货补货中", "phone": "19301178245", "exchangeType": "tencent", "time": "2025-07-03 02:41:18", "fullUserId": "3057660661"}]