#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小米钱包兑换会员配置文件
"""

# ==================== 兑换基本设置 ====================
# 兑换会员类型 (目前支持: iqiyi/tencent/youku/mango)
EXCHANGE_TYPE = "iqiyi"

# 兑换条件设置
MIN_DAYS_REQUIRED = 31  # 最少需要多少天才能兑换

# ==================== Web界面设置 ====================
# 最大显示账号数量（防止页面卡顿）
MAX_ACCOUNTS_DISPLAY = 100

# 默认显示账号数量
DEFAULT_ACCOUNTS_DISPLAY = 50

# 每批处理账号数量（异步加载时使用）
BATCH_SIZE = 10
EXCHANGE_HOUR = -1     # 兑换时间（24小时制，设置为-1表示任何时间都可以）

# 强制兑换模式（忽略时间和历史兑换检查）
FORCE_EXCHANGE = True

# ==================== 高级设置 ====================
# 是否忽略兑换历史（True=允许重复兑换，False=只允许首次兑换）
IGNORE_EXCHANGE_HISTORY = True

# 批量兑换间隔时间（秒）
EXCHANGE_INTERVAL = 2

# 最大重试次数
MAX_RETRY_COUNT = 3
