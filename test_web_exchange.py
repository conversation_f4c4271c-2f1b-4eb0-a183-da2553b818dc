#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web兑换接口
"""

import requests
import json

def test_web_exchange():
    print("🧪 测试Web兑换接口")
    print("=" * 60)
    
    # 测试数据
    test_data = {
        "phone": "18348185287",
        "userId": "3057660427",  # 36.18天的账号
        "exchangeType": "tencent"
    }
    
    print(f"📱 测试手机号: {test_data['phone']}")
    print(f"👤 测试用户ID: {test_data['userId']}")
    print(f"🎬 兑换类型: {test_data['exchangeType']}")
    
    # 发送兑换请求
    url = "http://127.0.0.1:5000/api/exchange"
    
    try:
        print(f"\n🚀 发送兑换请求到: {url}")
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应数据:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if result.get('success'):
                print(f"\n🎉 兑换成功！")
                print(f"💬 消息: {result.get('message')}")
                
                # 显示后端返回信息
                raw_response = result.get('rawResponse', {})
                if raw_response:
                    print(f"\n🔧 后端返回信息:")
                    print(json.dumps(raw_response, indent=2, ensure_ascii=False))
            else:
                print(f"\n❌ 兑换失败")
                print(f"💬 消息: {result.get('message')}")
                
                # 显示错误详情
                raw_response = result.get('rawResponse', {})
                if raw_response:
                    print(f"\n🔧 后端返回信息:")
                    print(json.dumps(raw_response, indent=2, ensure_ascii=False))
                    
                    # 分析错误
                    if 'current' in raw_response and 'required' in raw_response:
                        current = raw_response['current']
                        required = raw_response['required']
                        print(f"\n📊 积分分析:")
                        print(f"   当前积分: {current}天")
                        print(f"   需要积分: {required}天")
                        print(f"   差额: {required - current}天")
                        
                        if current == 0:
                            print(f"⚠️ 当前积分为0，可能是参数问题或查询失败")
                        else:
                            print(f"✅ 积分查询正常")
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

if __name__ == "__main__":
    test_web_exchange()
