#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的API接口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web_server import get_xiaomi_cookies, XiaomiExchanger, load_accounts_from_file, mask_user_id

def test_new_apis():
    print("🧪 测试新的API接口")
    print("=" * 60)
    
    # 加载账号
    accounts = load_accounts_from_file()
    if not accounts:
        print("❌ 未找到账号信息")
        return
    
    account = accounts[0]
    print(f"📱 测试账号: {account['phone']}")
    print(f"👤 用户ID: {mask_user_id(account['userId'])}")
    
    # 获取cookies
    print("\n🔐 步骤1: 获取cookies")
    cookies = get_xiaomi_cookies(account['passToken'], account['userId'])
    
    if not cookies:
        print("❌ cookies获取失败")
        return
    
    print("✅ cookies获取成功")
    
    # 创建兑换器
    exchanger = XiaomiExchanger(cookies)
    
    # 测试用户信息查询
    print("\n📊 步骤2: 测试用户信息查询")
    if exchanger.query_user_info():
        print(f"💎 总天数: {exchanger.total_days}")
        print(f"📜 兑换历史: {'已兑换过' if exchanger.has_exchanged_before else '从未兑换'}")
    else:
        print("❌ 用户信息查询失败")
        return
    
    # 测试兑换历史查询（新接口）
    print("\n📋 步骤3: 测试兑换历史查询（myPrize接口）")
    url = f'https://m.jr.airstarfinance.net/mp/api/generalActivity/myPrize?isNfcPhone=false&channel=local&deviceType=2&system=1&visitEnvironment=2&userExtra=%7B%22platformType%22:1,%22com.miui.player%22:%**********%22,%22com.miui.video%22:%22v2024090290(MiVideo-UN)%22,%22com.mipay.wallet%22:%226.83.0.5175.2256%22%7D&activityCode=2211-videoWelfare'
    
    history_res = exchanger.rr.get(url)
    
    if history_res and history_res.get('code') == 0:
        prize_list = history_res.get('value', {}).get('prizeInfoList', [])
        print(f"✅ 兑换历史查询成功，找到 {len(prize_list)} 条记录")
        
        for i, prize in enumerate(prize_list, 1):
            prize_name = prize.get('prizeName', '未知奖品')
            phone = prize.get('userPhone', '未知手机号')
            create_time = prize.get('createTime', 0)
            status = prize.get('status', 0)
            convert_num = prize.get('convertNum', 0)
            
            # 转换时间戳
            if create_time:
                import datetime
                time_str = datetime.datetime.fromtimestamp(create_time/1000).strftime('%Y-%m-%d %H:%M:%S')
            else:
                time_str = '未知时间'
            
            status_text = '成功' if status == 2 else f'状态{status}'
            convert_days = convert_num / 100
            
            print(f"📋 记录{i}: {prize_name} -> {phone}")
            print(f"    🕐 时间: {time_str}")
            print(f"    ✅ 状态: {status_text}")
            print(f"    💰 消耗: {convert_days}天")
            print(f"    🎁 代码: {prize.get('prizeCode', '未知')}")
            print()
    else:
        error_msg = history_res.get('error', '查询失败') if history_res else '无响应'
        print(f"❌ 兑换历史查询失败: {error_msg}")
    
    # 测试兑换接口（如果积分足够）
    print("\n🎬 步骤4: 测试兑换接口")
    if exchanger.total_days >= 31:
        print(f"✅ 积分足够（{exchanger.total_days}天），可以测试兑换")
        
        # 询问是否真的要兑换
        response = input("⚠️ 是否真的要进行兑换测试？这会消耗31天积分！(y/N): ")
        if response.lower() == 'y':
            test_phone = "18348185287"
            exchange_type = "tencent"
            
            print(f"🚀 开始兑换测试...")
            success, message, raw_response = exchanger.exchange_member(test_phone, exchange_type)
            
            print(f"\n📋 兑换结果:")
            print(f"✅ 成功: {success}")
            print(f"💬 消息: {message}")
            print(f"🔧 原始响应:")
            import json
            print(json.dumps(raw_response, indent=2, ensure_ascii=False))
        else:
            print("⏭️ 跳过兑换测试")
    else:
        print(f"⚠️ 积分不足（{exchanger.total_days}天 < 31天），跳过兑换测试")
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    test_new_apis()
