#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小米钱包兑换会员 Web 服务器
"""

from flask import Flask, render_template, request, jsonify
import json
import time
import os
from datetime import datetime
import requests
import urllib3
from typing import Optional, Dict, Any, Union

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

app = Flask(__name__)

# 导入兑换相关模块
try:
    from 兑换配置 import (
        EXCHANGE_TYPE, MIN_DAYS_REQUIRED,
        MAX_ACCOUNTS_DISPLAY, DEFAULT_ACCOUNTS_DISPLAY, BATCH_SIZE
    )
except ImportError:
    EXCHANGE_TYPE = "iqiyi"
    MIN_DAYS_REQUIRED = 31
    MAX_ACCOUNTS_DISPLAY = 100
    DEFAULT_ACCOUNTS_DISPLAY = 50
    BATCH_SIZE = 10

class RnlRequest:
    def __init__(self, cookies: Union[str, dict]):
        self.session = requests.Session()
        self._base_headers = {
            'Host': 'm.jr.airstarfinance.net',
            'User-Agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2012K11AC Build/UKQ1.230804.001; AppBundle/com.mipay.wallet; AppVersionName/6.89.1.5275.2323; AppVersionCode/********; MiuiVersion/stable-V816.0.13.0.UMNCNXM; DeviceId/alioth; NetworkType/WIFI; mix_version; WebViewVersion/*********) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        }
        self.update_cookies(cookies)

    def request(self, method: str, url: str, params: Optional[Dict[str, Any]] = None, data: Optional[Union[Dict[str, Any], str, bytes]] = None, json: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[Dict[str, Any]]:
        headers = {**self._base_headers, **kwargs.pop('headers', {})}
        try:
            # 添加调试信息
            if 'exchange' in url:
                print(f"🔍 请求方法: {method.upper()}")
                print(f"🔍 请求URL: {url}")
                print(f"🔍 请求头: {headers}")
                print(f"🔍 Cookies: {dict(self.session.cookies)}")

            resp = self.session.request(verify=False, method=method.upper(), url=url, params=params, data=data, json=json, headers=headers, timeout=30, **kwargs)

            if 'exchange' in url:
                print(f"🔍 响应状态码: {resp.status_code}")
                print(f"🔍 响应头: {dict(resp.headers)}")
                print(f"🔍 响应内容: {resp.text[:500]}...")

            resp.raise_for_status()
            return resp.json()
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
            return None
        except requests.exceptions.ConnectionError:
            print("🌐 连接错误")
            return None
        except requests.exceptions.HTTPError as e:
            print(f"📡 HTTP错误: {e}")
            return None
        except Exception as e:
            print(f"⚠️ 请求异常: {str(e)}")
            return None

    def update_cookies(self, cookies: Union[str, dict]) -> None:
        if cookies:
            if isinstance(cookies, str):
                dict_cookies = self._parse_cookies(cookies)
            else:
                dict_cookies = cookies
            self.session.cookies.update(dict_cookies)
            self._base_headers['Cookie'] = self.dict_cookie_to_string(dict_cookies)

    @staticmethod
    def _parse_cookies(cookies_str: str) -> Dict[str, str]:
        return dict(item.strip().split('=', 1) for item in cookies_str.split(';') if '=' in item)

    @staticmethod
    def dict_cookie_to_string(cookie_dict):
        return "; ".join([f"{key}={value}" for key, value in cookie_dict.items()])

    def get(self, url: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[Dict[str, Any]]:
        return self.request('GET', url, params=params, **kwargs)

class XiaomiExchanger:
    def __init__(self, cookies, activity_type='video'):
        # 根据活动类型设置不同的参数
        self.activity_type = activity_type
        if activity_type == 'music':
            self.activity_code = 'qq-music-201303'
            self.exchange_codes = {
                'iqiyi': 'iqiyi-31',
                'tencent': 'tencent-31',
                'youku': 'youku-31',
                'mango': 'mango-31'
            }
        else:  # video
            self.activity_code = '2211-videoWelfare'
            self.exchange_codes = {
                'iqiyi': 'iqiyi-31',
                'tencent': 'tencent-31',
                'youku': 'youku-31',
                'mango': 'mango-31'
            }

        self.rr = RnlRequest(cookies)
        self.total_days = 0.0
        self.has_exchanged_before = False  # 不再限制已兑换过的账号

    def query_user_info(self):
        try:
            # 使用正确的参数（与真实请求一致）
            url = f'https://m.jr.airstarfinance.net/mp/api/generalActivity/queryUserGoldRichSum?app=com.mipay.wallet&deviceType=0&system=0&visitEnvironment=1&userExtra=%7B%22platformType%22:4,%22com.miui.player%22:%22notSupportGetVersionName%22,%22com.miui.video%22:%22notSupportGetVersionName%22,%22com.xiaomi.jr%22:%22notSupportGetVersionName%22,%22com.mipay.wallet%22:%22notSupportGetVersionName%22%7D&activityCode={self.activity_code}'
            total_res = self.rr.get(url)
            if total_res and total_res['code'] == 0:
                # 音乐会员返回的是分钟数，需要转换为天数
                if self.activity_type == 'music':
                    total_minutes = total_res['value']
                    self.total_days = total_minutes / (24 * 60)  # 转换为天数
                else:
                    self.total_days = int(total_res['value']) / 100
            # 检查兑换历史（但不限制兑换）
            self.has_exchanged_before = self.check_exchange_history()
            print(f"📜 {self.activity_type} 兑换历史检查: {'已兑换过' if self.has_exchanged_before else '从未兑换'}，但不限制兑换")
            return True
        except Exception as e:
            print(f"查询用户信息失败: {e}")
            return False

    def check_exchange_history(self):
        """检查兑换历史记录，判断是否曾经兑换过"""
        try:
            # 使用新的myPrize接口查询兑换记录
            # 使用正确的参数（与真实请求一致）
            url = f'https://m.jr.airstarfinance.net/mp/api/generalActivity/myPrize?isNfcPhone=false&channel=local&deviceType=0&system=0&visitEnvironment=1&userExtra=%7B%22platformType%22:4,%22com.miui.player%22:%22notSupportGetVersionName%22,%22com.miui.video%22:%22notSupportGetVersionName%22,%22com.xiaomi.jr%22:%22notSupportGetVersionName%22,%22com.mipay.wallet%22:%22notSupportGetVersionName%22%7D&activityCode={self.activity_code}'

            print(f"🔍 查询兑换历史: {url}")
            history_res = self.rr.get(url)

            if history_res and history_res.get('code') == 0:
                prize_list = history_res.get('value', {}).get('prizeInfoList', [])
                print(f"✅ 兑换历史查询成功，找到 {len(prize_list)} 条记录")

                # 打印兑换记录详情
                for prize in prize_list:
                    prize_name = prize.get('prizeName', '未知奖品')
                    phone = prize.get('userPhone', '未知手机号')
                    create_time = prize.get('createTime', 0)
                    status = prize.get('status', 0)

                    # 转换时间戳
                    if create_time:
                        import datetime
                        time_str = datetime.datetime.fromtimestamp(create_time/1000).strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        time_str = '未知时间'

                    status_text = '成功' if status == 2 else f'状态{status}'

                    # 显示API返回的原始奖品名称（用于调试）
                    print(f"📋 兑换记录: {prize_name} -> {phone} | {time_str} | {status_text}")

                return len(prize_list) > 0
            else:
                error_msg = history_res.get('error', '未知错误') if history_res else '无响应'
                print(f"⚠️ 兑换历史查询失败: {error_msg}")
                return False

        except Exception as e:
            print(f"⚠️ 兑换历史查询异常: {str(e)}")
            return False

    def exchange_member(self, phone: str, exchange_type: str) -> tuple[bool, str, dict]:
        try:
            # 根据活动类型设置不同的prizeCode映射
            if self.activity_type == 'music':
                # 音乐会员只有一种类型，统一使用 tencent-31
                prize_code = 'tencent-31'  # 音乐VIP月卡
            else:
                # 视频会员的prizeCode映射（根据实际测试确认）
                prize_code_map = {
                    'tencent': 'LSXD_PRIZE1263',  # 腾讯视频VIP月卡 (已确认，需要31天)
                    'iqiyi': 'LSXD_PRIZE1264',    # 爱奇艺黄金会员月卡 (推测)
                    'youku': 'LSXD_PRIZE1265',    # 优酷VIP会员月卡 (推测)
                    'mango': 'LSXD_PRIZE1266'     # 芒果TV会员月卡 (推测)
                }
                prize_code = prize_code_map.get(exchange_type, 'LSXD_PRIZE1263')

            required_days = 31.0  # 音乐和视频会员都需要31天

            # 检查积分是否足够（不再检查兑换历史，只要天数够就可以兑换）
            if self.total_days < required_days:
                error_msg = f"积分不足，需要{required_days}天，当前只有{self.total_days:.1f}天"
                print(f"❌ {error_msg}")
                return False, error_msg, {"error": "积分不足", "required": required_days, "current": self.total_days}

            print(f"✅ 积分充足（{self.total_days:.1f}天 >= {required_days}天），可以兑换")

            # 使用新的convertGoldRich接口（使用正确的参数）
            url = f"https://m.jr.airstarfinance.net/mp/api/generalActivity/convertGoldRich?prizeCode={prize_code}&activityCode={self.activity_code}&phone={phone}&isNfcPhone=false&channel=exchange&deviceType=0&system=0&visitEnvironment=1&userExtra=%7B%22platformType%22:4,%22com.miui.player%22:%22notSupportGetVersionName%22,%22com.miui.video%22:%22notSupportGetVersionName%22,%22com.xiaomi.jr%22:%22notSupportGetVersionName%22,%22com.mipay.wallet%22:%22notSupportGetVersionName%22%7D"

            print(f"🔗 兑换请求URL: {url}")
            print(f"📱 兑换手机号: {phone}")
            print(f"🎬 兑换类型: {exchange_type}")
            print(f"🎁 奖品代码: {prize_code}")

            # 发起兑换请求
            response = self.rr.get(url)

            print(f"📡 原始响应: {response}")

            # 返回原始响应
            raw_response = response if response else {"error": "无响应", "url": url}

            if response:
                if response.get('code') == 0 and response.get('success') == True:
                    print("✅ 兑换请求成功")
                    prize_info = response.get('value', {}).get('prizeInfo', {})
                    prize_desc = prize_info.get('prizeDesc', '会员')
                    return True, f"兑换成功！获得{prize_desc}", raw_response
                else:
                    error_msg = response.get('error', response.get('message', '兑换失败'))
                    print(f"❌ 兑换失败: {error_msg}")
                    return False, f"兑换失败: {error_msg}", raw_response
            else:
                print("❌ 无响应 - 可能是网络问题或认证失败")
                return False, "兑换失败: 无响应", raw_response
        except Exception as e:
            print(f"⚠️ 兑换请求异常: {str(e)}")
            error_response = {"error": str(e), "exception": True, "url": url if 'url' in locals() else "未知"}
            return False, f"兑换请求异常: {str(e)}", error_response

def get_xiaomi_cookies(pass_token, user_id):
    """获取小米钱包cookies"""
    login_url = 'https://account.xiaomi.com/pass/serviceLogin?callback=https%3A%2F%2Fapi.jr.airstarfinance.net%2Fsts%3Fsign%3D1dbHuyAmee0NAZ2xsRw5vhdVQQ8%253D%26followup%3Dhttps%253A%252F%252Fm.jr.airstarfinance.net%252Fmp%252Fapi%252Flogin%253Ffrom%253Dmipay_indexicon_TVcard%2526deepLinkEnable%253Dfalse%2526requestUrl%253Dhttps%25253A%25252F%25252Fm.jr.airstarfinance.net%25252Fmp%25252Factivity%25252FvideoActivity%25253Ffrom%25253Dmipay_indexicon_TVcard%252526_noDarkMode%25253Dtrue%252526_transparentNaviBar%25253Dtrue%252526cUserId%25253Dusyxgr5xjumiQLUoAKTOgvi858Q%252526_statusBarHeight%25253D137&sid=jrairstar&_group=DEFAULT&_snsNone=true&_loginType=ticket'
    headers = {
        'user-agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2012K11AC Build/UKQ1.230804.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/100.0.4896.127 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'cookie': f'passToken={pass_token}; userId={user_id};'
    }

    print(f"🔐 开始获取cookies - userId: {mask_user_id(user_id)}")
    print(f"🔐 passToken长度: {len(pass_token)}")

    try:
        session = requests.Session()
        resp = session.get(url=login_url, headers=headers, verify=False, timeout=10)

        print(f"🔐 登录响应状态: {resp.status_code}")

        cookies = session.cookies.get_dict()
        print(f"🔐 获取到的cookies: {list(cookies.keys())}")

        if 'cUserId' in cookies and 'serviceToken' in cookies:
            result_cookies = f"cUserId={cookies.get('cUserId')};jrairstar_serviceToken={cookies.get('serviceToken')}"
            print(f"✅ cookies获取成功")
            return result_cookies
        else:
            print(f"❌ cookies获取失败 - 缺少必要字段")
            print(f"🔍 可用cookies: {cookies}")
            return None
    except Exception as e:
        print(f"❌ cookies获取异常: {str(e)}")
        return None

def load_accounts_from_file():
    accounts = []
    try:
        with open('ck.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            parts = line.split('----')
            if len(parts) < 5:
                continue
            phone = parts[0].strip()
            cookies = parts[1].strip()
            pass_token_section = parts[4].strip()
            pass_token = ""
            user_id = ""
            token_parts = pass_token_section.split(';')
            for token_part in token_parts:
                token_part = token_part.strip()
                if token_part.startswith('passToken='):
                    pass_token = token_part.split('passToken=')[1].strip()
                elif token_part.startswith('userId='):
                    user_id = token_part.split('userId=')[1].strip()
            if not user_id:
                continue
            accounts.append({'passToken': pass_token, 'userId': user_id, 'phone': phone})
        return accounts
    except:
        return []

def mask_user_id(user_id):
    if len(user_id) <= 6:
        return '*' * len(user_id)
    return user_id[:3] + '*' * 6 + user_id[-3:]

def save_exchange_record(record):
    """保存兑换记录到JSON文件"""
    try:
        # 读取现有记录
        try:
            with open('exchange_records.json', 'r', encoding='utf-8') as f:
                records = json.load(f)
        except FileNotFoundError:
            records = []

        # 添加新记录
        records.insert(0, record)  # 最新记录在前

        # 保持最多100条记录
        if len(records) > 100:
            records = records[:100]

        # 保存到文件
        with open('exchange_records.json', 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)

    except Exception as e:
        print(f"保存兑换记录失败: {e}")

def load_exchange_records():
    """从JSON文件加载兑换记录"""
    try:
        with open('exchange_records.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []
    except Exception as e:
        print(f"加载兑换记录失败: {e}")
        return []

def get_exchanged_user_ids():
    """获取已兑换过的用户ID列表"""
    records = load_exchange_records()
    exchanged_ids = set()
    for record in records:
        if record.get('success') and record.get('fullUserId'):
            exchanged_ids.add(record['fullUserId'])
    return exchanged_ids

def check_prize_stock():
    url = "https://m.jr.airstarfinance.net/mp/api/generalActivity/getPrizeStatusV2"
    params = {"activityCode": "2211-videoWelfare", "needPrizeBrand": "youku,mgtv,iqiyi,tencent,bilibili,other"}
    headers = {"User-Agent": "Mozilla/5.0 (Linux; Android 13) AppleWebKit/537.36"}
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        prizes = response.json().get("value", [])
        stock_info = {}
        type_mapping = {"iqiyi": "爱奇艺黄金会员月卡", "tencent": "腾讯视频VIP月卡", "youku": "优酷VIP会员月卡", "mango": "芒果TV会员月卡", "bilibili": "哔哩哔哩会员月卡"}
        for prize in prizes:
            name = prize.get("prizeName", "").replace("\n", " ").strip()
            for key, value in type_mapping.items():
                if name == value:
                    status = prize.get("todayStockStatus", 2)
                    stock_info[key] = {"name": name, "has_stock": status == 1}
        return stock_info
    except:
        return {}

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/test')
def test():
    with open('test_api.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/api/exchange-records')
def get_exchange_records():
    """获取兑换记录"""
    records = load_exchange_records()
    return jsonify(records)

@app.route('/api/exchange-records', methods=['DELETE'])
def clear_exchange_records():
    """清空兑换记录"""
    try:
        with open('exchange_records.json', 'w', encoding='utf-8') as f:
            json.dump([], f)
        return jsonify({'success': True, 'message': '兑换记录已清空'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'清空失败: {str(e)}'}), 500

@app.route('/api/account/<user_id>/exchange-history')
def get_account_exchange_history(user_id):
    """获取指定账号的兑换历史（使用myPrize接口）"""
    try:
        # 获取账号信息
        accounts = load_accounts_from_file()
        account = next((acc for acc in accounts if acc['userId'] == user_id), None)

        if not account:
            return jsonify({'error': '账号不存在'}), 404

        # 获取cookies
        cookies = get_xiaomi_cookies(account['passToken'], account['userId'])
        if not cookies:
            return jsonify({'error': 'cookies获取失败'}), 500

        # 创建兑换器并查询历史
        exchanger = XiaomiExchanger(cookies)

        # 使用myPrize接口查询
        # 使用正确的参数（与真实请求一致）
        url = f'https://m.jr.airstarfinance.net/mp/api/generalActivity/myPrize?isNfcPhone=false&channel=local&deviceType=0&system=0&visitEnvironment=1&userExtra=%7B%22platformType%22:4,%22com.miui.player%22:%22notSupportGetVersionName%22,%22com.miui.video%22:%22notSupportGetVersionName%22,%22com.xiaomi.jr%22:%22notSupportGetVersionName%22,%22com.mipay.wallet%22:%22notSupportGetVersionName%22%7D&activityCode=2211-videoWelfare'

        history_res = exchanger.rr.get(url)

        if history_res and history_res.get('code') == 0:
            prize_list = history_res.get('value', {}).get('prizeInfoList', [])

            # 格式化兑换记录
            formatted_records = []
            for prize in prize_list:
                # 转换时间戳
                create_time = prize.get('createTime', 0)
                if create_time:
                    import datetime
                    time_str = datetime.datetime.fromtimestamp(create_time/1000).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    time_str = '未知时间'

                formatted_records.append({
                    'prizeName': prize.get('prizeName', '未知奖品'),
                    'prizeDesc': prize.get('prizeDesc', ''),
                    'userPhone': prize.get('userPhone', ''),
                    'createTime': create_time,
                    'timeStr': time_str,
                    'status': prize.get('status', 0),
                    'statusText': '成功' if prize.get('status') == 2 else f"状态{prize.get('status', 0)}",
                    'convertNum': prize.get('convertNum', 0),
                    'convertDays': prize.get('convertNum', 0) / 100,
                    'prizeCode': prize.get('prizeCode', ''),
                    'couponId': prize.get('couponId', '')
                })

            return jsonify({
                'success': True,
                'records': formatted_records,
                'total': len(formatted_records),
                'userId': mask_user_id(user_id)
            })
        else:
            error_msg = history_res.get('error', '查询失败') if history_res else '无响应'
            return jsonify({'error': error_msg}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def load_days_cache():
    """加载天数缓存"""
    try:
        if os.path.exists('days_cache.json'):
            with open('days_cache.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"❌ 加载天数缓存失败: {e}")
        return {}

def save_days_cache(cache_data):
    """保存天数缓存"""
    try:
        with open('days_cache.json', 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)
        print(f"💾 天数缓存已保存，共 {len(cache_data)} 条记录")
    except Exception as e:
        print(f"❌ 保存天数缓存失败: {e}")

def get_cached_days(user_id, activity_type='video'):
    """获取缓存的天数信息（永久缓存，直到手动刷新）"""
    cache = load_days_cache()
    cache_key = f"{user_id}_{activity_type}"

    if cache_key in cache:
        cached_data = cache[cache_key]
        return cached_data.get('days'), cached_data.get('hasExchanged', False)

    return None, None

def update_days_cache(user_id, activity_type, days, has_exchanged):
    """更新天数缓存"""
    cache = load_days_cache()
    cache_key = f"{user_id}_{activity_type}"

    cache[cache_key] = {
        'days': days,
        'hasExchanged': has_exchanged,
        'timestamp': datetime.now().isoformat(),
        'activityType': activity_type
    }

    save_days_cache(cache)

def get_current_month_exchanged_accounts(activity_type='video'):
    """获取当月已兑换的账号列表"""
    try:
        if not os.path.exists('exchange_records.json'):
            return set()

        with open('exchange_records.json', 'r', encoding='utf-8') as f:
            records = json.load(f)

        current_month = datetime.now().strftime('%Y-%m')
        exchanged_accounts = set()

        for record in records:
            # 检查是否是当月兑换且活动类型匹配
            record_time = record.get('time', '')
            record_activity = record.get('activityType', 'video')
            record_success = record.get('success', False)

            if (record_time.startswith(current_month) and
                record_activity == activity_type and
                record_success):
                # 使用完整用户ID标识已兑换账号
                exchanged_accounts.add(record.get('fullUserId'))

        print(f"📅 {activity_type} 当月({current_month})已兑换账号: {len(exchanged_accounts)} 个")
        return exchanged_accounts
    except Exception as e:
        print(f"❌ 获取当月兑换记录失败: {e}")
        return set()

@app.route('/api/accounts')
def get_accounts():
    """获取可用账号列表（智能过滤版本）"""
    try:
        all_accounts = load_accounts_from_file()

        # 获取请求参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 5, type=int)  # 每页5个
        activity_type = request.args.get('activity_type', 'video')  # 活动类型

        # 获取当月已兑换的账号
        exchanged_accounts = get_current_month_exchanged_accounts(activity_type)

        # 第一步：分离已兑换和未兑换的账号
        not_exchanged = []
        exchanged_this_month = []

        for account in all_accounts:
            if account['userId'] in exchanged_accounts:
                exchanged_this_month.append(account)
            else:
                not_exchanged.append(account)

        print(f"📊 总账号: {len(all_accounts)}, 当月未兑换: {len(not_exchanged)}, 当月已兑换: {len(exchanged_this_month)}")

        # 第二步：使用缓存优先的方式获取账号天数
        valid_accounts = []  # 天数≥31的账号
        force_refresh = request.args.get('force_refresh', 'false').lower() == 'true'

        # 先添加已兑换的账号（使用缓存天数，不查询）
        for account in exchanged_this_month:
            user_id = account['userId']
            cached_days, cached_exchanged = get_cached_days(user_id, activity_type)

            # 使用缓存天数，如果没有缓存则假设有足够天数
            account['totalDays'] = cached_days if cached_days is not None else 31.0
            account['hasExchanged'] = True
            account['status'] = 'exchanged_this_month'
            account['skipReason'] = f'当月已兑换{activity_type}会员'

            # 只有天数≥31的当月已兑换账号才显示
            if account['totalDays'] >= 31.0:
                valid_accounts.append(account)
            else:
                print(f"⏭️ {mask_user_id(user_id)}: {account['totalDays']:.1f}天 (当月已兑换但天数不足，不显示)")

        # 对未兑换的账号进行天数检查（优先使用缓存）
        print(f"🔍 检查 {len(not_exchanged)} 个未兑换账号的天数（缓存优先）...")

        # 分离有缓存和无缓存的账号
        cached_accounts = []
        uncached_accounts = []

        for account in not_exchanged:
            user_id = account['userId']
            cached_days, cached_exchanged = get_cached_days(user_id, activity_type)

            if cached_days is not None and not force_refresh:
                # 有缓存数据
                if cached_days >= 31.0:
                    account['totalDays'] = cached_days
                    account['hasExchanged'] = cached_exchanged
                    account['status'] = 'cached'
                    cached_accounts.append(account)
                    print(f"📋 {mask_user_id(user_id)}: {cached_days:.1f}天 (缓存)")
                # 缓存显示<31天的账号直接跳过，不显示
            else:
                # 无缓存数据，需要查询
                uncached_accounts.append(account)

        # 先添加有缓存的符合条件账号
        valid_accounts.extend(cached_accounts)

        # 如果强制刷新或需要更多账号来填满页面，才查询无缓存的账号
        if force_refresh or len(valid_accounts) < per_page * 3:  # 保证至少有3页的数据
            print(f"🔍 查询 {len(uncached_accounts)} 个无缓存账号...")

            for account in uncached_accounts:
                user_id = account['userId']
                try:
                    cookies = get_xiaomi_cookies(account['passToken'], user_id)
                    if cookies:
                        exchanger = XiaomiExchanger(cookies, activity_type)
                        if exchanger.query_user_info():
                            # 更新缓存
                            update_days_cache(user_id, activity_type, exchanger.total_days, exchanger.has_exchanged_before)

                            if exchanger.total_days >= 31.0:
                                account['totalDays'] = exchanger.total_days
                                account['hasExchanged'] = exchanger.has_exchanged_before
                                account['status'] = 'fresh'
                                valid_accounts.append(account)
                                print(f"✅ {mask_user_id(user_id)}: {exchanger.total_days:.1f}天 (实时查询)")
                            else:
                                print(f"❌ {mask_user_id(user_id)}: {exchanger.total_days:.1f}天 (不符合条件)")
                        else:
                            print(f"❌ {mask_user_id(user_id)}: 查询失败")
                    else:
                        print(f"❌ {mask_user_id(user_id)}: 登录失败")
                except Exception as e:
                    print(f"❌ {mask_user_id(user_id)}: 查询失败 - {e}")

                # 如果已经有足够的账号，停止查询
                if len(valid_accounts) >= per_page * 10:  # 保证有10页的数据就够了
                    print(f"⏹️ 已获取足够账号，停止查询")
                    break

        print(f"📊 过滤后有效账号: {len(valid_accounts)} 个")

        # 第三步：分页显示
        total_accounts = len(valid_accounts)
        total_pages = (total_accounts + per_page - 1) // per_page if total_accounts > 0 else 1
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page

        page_accounts = valid_accounts[start_idx:end_idx]

        print(f"📊 第 {page}/{total_pages} 页，显示 {len(page_accounts)} 个账号")

        # 返回当前页的账号基本信息
        result = []
        for account in page_accounts:
            result.append({
                'userId': account['userId'],
                'maskedUserId': mask_user_id(account['userId']),
                'phone': account['phone'],
                'totalDays': 0,  # 初始值，需要单独查询
                'hasExchanged': False,  # 初始值，需要单独查询
                'passToken': account['passToken'][:20] + '...',
                'status': 'pending'  # 待查询状态
            })

        return jsonify({
            'accounts': result,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_accounts,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages
            }
        })
    except Exception as e:
        return jsonify({'error': f'加载账号失败: {str(e)}'}), 500

@app.route('/api/account/<user_id>/details')
def get_account_details(user_id):
    """获取单个账号的详细信息（简化版本，主要用于兑换时的最终确认）"""
    accounts = load_accounts_from_file()
    account = next((acc for acc in accounts if acc['userId'] == user_id), None)

    if not account:
        return jsonify({'error': '账号不存在'}), 404

    # 获取活动类型参数
    activity_type = request.args.get('activity_type', 'video')

    # 检查是否为当月已兑换账号
    exchanged_accounts = get_current_month_exchanged_accounts(activity_type)
    if user_id in exchanged_accounts:
        print(f"⏭️ 当月已兑换账号: {mask_user_id(user_id)} ({activity_type})")
        return jsonify({
            'userId': account['userId'],
            'maskedUserId': mask_user_id(account['userId']),
            'phone': account['phone'],
            'totalDays': 31.0,  # 已兑换账号假设有足够天数
            'hasExchanged': True,
            'passToken': account['passToken'][:20] + '...',
            'status': 'exchanged_this_month',
            'canExchange': True,  # 仍然可以兑换，只是当月已兑换过
            'activityType': activity_type,
            'skipReason': f'当月已兑换{activity_type}会员'
        })

    # 如果账号已经有天数信息（从列表页面传递过来），直接返回
    if hasattr(account, 'totalDays'):
        return jsonify({
            'userId': account['userId'],
            'maskedUserId': mask_user_id(account['userId']),
            'phone': account['phone'],
            'totalDays': round(account['totalDays'], 1),
            'hasExchanged': getattr(account, 'hasExchanged', False),
            'passToken': account['passToken'][:20] + '...',
            'status': 'loaded',
            'canExchange': account['totalDays'] >= MIN_DAYS_REQUIRED,
            'activityType': activity_type
        })

    # 否则重新查询
    try:
        cookies = get_xiaomi_cookies(account['passToken'], account['userId'])
        if not cookies:
            return jsonify({'error': '登录失败'}), 400

        exchanger = XiaomiExchanger(cookies, activity_type)
        if not exchanger.query_user_info():
            return jsonify({'error': '查询失败'}), 400

        return jsonify({
            'userId': account['userId'],
            'maskedUserId': mask_user_id(account['userId']),
            'phone': account['phone'],
            'totalDays': round(exchanger.total_days, 1),
            'hasExchanged': exchanger.has_exchanged_before,
            'passToken': account['passToken'][:20] + '...',
            'status': 'loaded',
            'canExchange': exchanger.total_days >= MIN_DAYS_REQUIRED,
            'activityType': activity_type
        })

    except Exception as e:
        return jsonify({'error': f'查询异常: {str(e)}'}), 500

@app.route('/api/stock')
def get_stock():
    return jsonify(check_prize_stock())

@app.route('/api/refresh-days', methods=['POST'])
def refresh_days():
    """强制刷新所有账号天数"""
    try:
        data = request.json or {}
        activity_type = data.get('activityType', 'video')

        accounts = load_accounts_from_file()
        updated_count = 0
        failed_count = 0

        print(f"🔄 开始强制刷新 {len(accounts)} 个账号的{activity_type}天数...")

        for account in accounts:
            try:
                cookies = get_xiaomi_cookies(account['passToken'], account['userId'])
                if cookies:
                    exchanger = XiaomiExchanger(cookies, activity_type)
                    if exchanger.query_user_info():
                        # 强制更新缓存
                        update_days_cache(account['userId'], activity_type, exchanger.total_days, exchanger.has_exchanged_before)
                        updated_count += 1
                        print(f"✅ {mask_user_id(account['userId'])}: {exchanger.total_days:.1f}天")
                    else:
                        failed_count += 1
                        print(f"❌ {mask_user_id(account['userId'])}: 查询失败")
                else:
                    failed_count += 1
                    print(f"❌ {mask_user_id(account['userId'])}: 登录失败")
            except Exception as e:
                failed_count += 1
                print(f"❌ {mask_user_id(account['userId'])}: 异常 - {e}")

        return jsonify({
            'success': True,
            'message': f'刷新完成！成功: {updated_count}, 失败: {failed_count}',
            'updated': updated_count,
            'failed': failed_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新失败: {str(e)}'
        }), 500

@app.route('/api/exchange', methods=['POST'])
def exchange():
    data = request.json
    phone = data.get('phone')
    user_id = data.get('userId')
    exchange_type = data.get('exchangeType', 'iqiyi')  # 从前端获取兑换类型
    activity_type = data.get('activityType', 'video')  # 从前端获取活动类型

    accounts = load_accounts_from_file()
    account = next((acc for acc in accounts if acc['userId'] == user_id), None)

    if not account:
        return jsonify({'success': False, 'message': '账号不存在'})

    cookies = get_xiaomi_cookies(account['passToken'], account['userId'])
    if not cookies:
        return jsonify({'success': False, 'message': '登录失败'})

    exchanger = XiaomiExchanger(cookies, activity_type)

    # 先查询用户信息获取天数
    if not exchanger.query_user_info():
        return jsonify({'success': False, 'message': '用户信息查询失败'})

    print(f"💎 用户天数: {exchanger.total_days}")
    print(f"📜 兑换历史: {'已兑换过' if exchanger.has_exchanged_before else '从未兑换'}")

    success, message, raw_response = exchanger.exchange_member(phone, exchange_type)

    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 如果兑换成功，更新天数缓存（减去31天）
    if success:
        new_days = max(0, exchanger.total_days - 31.0)
        update_days_cache(user_id, activity_type, new_days, True)
        print(f"🔄 兑换成功，更新缓存: {mask_user_id(user_id)} 天数 {exchanger.total_days:.1f} → {new_days:.1f}")

    # 保存兑换记录到JSON文件
    save_exchange_record({
        'success': success,
        'message': message,
        'phone': phone,
        'exchangeType': exchange_type,
        'activityType': activity_type,  # 保存活动类型
        'time': current_time,
        'fullUserId': user_id,  # 保存完整用户ID用于过滤，但不显示
        'rawResponse': raw_response  # 保存原始响应
    })

    return jsonify({
        'success': success,
        'message': message,
        'phone': phone,
        'exchangeType': exchange_type,
        'time': current_time,
        'rawResponse': raw_response,  # 返回原始后端响应
        'accountInfo': {
            'maskedUserId': mask_user_id(user_id),
            'passToken': account['passToken'][:20] + '...'
        }
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
