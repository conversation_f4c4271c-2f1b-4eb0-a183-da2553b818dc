# 小米钱包兑换会员功能使用说明

## 文件说明

### 1. 主要文件
- `小米钱包兑换会员.py` - 兑换会员的主程序
- `兑换配置.py` - 兑换功能的配置文件
- `ck.txt` - 账号信息文件（与主程序共用）

### 2. 功能特点
- ✅ 独立的兑换功能，不依赖主程序
- ✅ 支持批量兑换多个账号
- ✅ 可配置的兑换条件和时间
- ✅ 支持重试机制
- ✅ 详细的兑换日志和统计

## 配置说明

### 基本配置（兑换配置.py）

```python
# 兑换会员类型
EXCHANGE_TYPE = "iqiyi"  # iqiyi/tencent/youku/mango

# 兑换条件
MIN_DAYS_REQUIRED = 7    # 最少需要多少天才能兑换
EXCHANGE_HOUR = 10       # 兑换时间（-1表示任何时间）

# 特殊模式
FORCE_EXCHANGE = False   # 强制兑换（忽略所有限制）
IGNORE_EXCHANGE_HISTORY = False  # 忽略兑换历史（允许重复兑换）

# 高级设置
EXCHANGE_INTERVAL = 2    # 兑换间隔时间（秒）
MAX_RETRY_COUNT = 3      # 最大重试次数
```

### 会员类型说明
- `iqiyi` - 爱奇艺会员
- `tencent` - 腾讯视频会员
- `youku` - 优酷会员
- `mango` - 芒果TV会员

## 使用方法

### 1. 基本使用
```bash
# 直接运行兑换程序
python 小米钱包兑换会员.py
```

### 2. 配置修改
1. 编辑 `兑换配置.py` 文件
2. 修改相应的配置项
3. 保存后运行兑换程序

### 3. 兑换模式

#### 普通模式（默认）
- 检查天数要求（≥7天）
- 检查兑换历史（只允许首次兑换）
- 检查兑换时间（默认10点）

#### 强制模式
```python
FORCE_EXCHANGE = True
```
- 忽略所有限制条件
- 任何时间都可以兑换
- 允许重复兑换

#### 自定义模式
```python
EXCHANGE_HOUR = -1              # 任何时间都可以兑换
IGNORE_EXCHANGE_HISTORY = True  # 允许重复兑换
MIN_DAYS_REQUIRED = 5           # 自定义最少天数
```

## 运行示例

### 示例1：正常兑换
```
🎁 小米钱包会员兑换专用脚本
==================================================
✅ 已加载兑换配置文件
✅ 找到 144 个账号
🎯 兑换会员类型: iqiyi
📋 最少天数要求: 7天
⏰ 兑换时间要求: 10点
🔧 强制兑换模式: 已关闭
📜 忽略兑换历史: 否
🔄 最大重试次数: 3次
==================================================
🕐 当前时间: 2024-07-02 10:15:30

▶️ 处理账号 1/144 (ID: 305******661, 手机: 19202517653)
💎 当前总天数: 8.5天
📜 兑换历史: 从未兑换
🎁 开始兑换iqiyi会员...
🎉 兑换成功
```

### 示例2：条件不满足
```
▶️ 处理账号 2/144 (ID: 305******427, 手机: 19202514279)
💎 当前总天数: 5.2天
📜 兑换历史: 从未兑换
⚠️ 不满足兑换条件: 总天数不足7天（当前5.2天）
```

### 示例3：重试机制
```
▶️ 处理账号 3/144 (ID: 305******656, 手机: 19212425803)
💎 当前总天数: 10.0天
📜 兑换历史: 从未兑换
🎁 开始兑换iqiyi会员...
⚠️ 兑换失败: 缺货补货中，2秒后重试...
🔄 第2次尝试...
🎉 兑换成功
```

## 注意事项

### 1. 兑换时间
- 建议在10点进行兑换（库存最充足）
- 可以设置 `EXCHANGE_HOUR = -1` 允许任何时间兑换
- 强制模式下忽略时间限制

### 2. 兑换条件
- 默认要求总天数≥7天
- 默认只允许首次兑换（防止重复兑换）
- 可以通过配置文件调整这些条件

### 3. 重试机制
- 兑换失败时会自动重试
- 默认最多重试3次
- 每次重试间隔2秒

### 4. 安全建议
- 不要设置过短的兑换间隔时间
- 建议在非高峰时间进行批量兑换
- 兑换失败通常是库存不足，建议稍后重试

## 常见问题

### Q: 兑换失败怎么办？
A: 通常是库存不足，可以：
1. 稍后重试
2. 更换兑换时间
3. 更换会员类型

### Q: 如何允许重复兑换？
A: 设置 `IGNORE_EXCHANGE_HISTORY = True`

### Q: 如何在任何时间兑换？
A: 设置 `EXCHANGE_HOUR = -1` 或 `FORCE_EXCHANGE = True`

### Q: 如何修改最少天数要求？
A: 修改 `MIN_DAYS_REQUIRED` 的值

### Q: 兑换间隔太快会怎样？
A: 可能被限制请求，建议保持2秒以上间隔
