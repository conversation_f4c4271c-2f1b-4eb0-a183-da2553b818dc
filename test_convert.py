#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的convertGoldRich兑换接口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web_server import get_xiaomi_cookies, XiaomiExchanger, load_accounts_from_file, mask_user_id

def test_convert_exchange():
    print("🧪 测试新的convertGoldRich兑换接口")
    print("=" * 60)
    
    # 加载账号
    accounts = load_accounts_from_file()
    if not accounts:
        print("❌ 未找到账号信息")
        return
    
    # 使用第一个账号进行测试
    account = accounts[0]
    print(f"📱 测试账号: {account['phone']}")
    print(f"👤 用户ID: {mask_user_id(account['userId'])}")
    
    # 获取cookies
    print("\n🔐 步骤1: 获取cookies")
    cookies = get_xiaomi_cookies(account['passToken'], account['userId'])
    
    if not cookies:
        print("❌ cookies获取失败")
        return
    
    print("✅ cookies获取成功")
    
    # 创建兑换器
    print("\n🎁 步骤2: 创建兑换器")
    exchanger = XiaomiExchanger(cookies)
    
    # 简化的用户信息查询（跳过兑换历史检查）
    print("\n📊 步骤3: 查询用户基本信息")
    try:
        # 只查询总天数，跳过兑换历史
        total_res = exchanger.rr.get('https://m.jr.airstarfinance.net/mp/api/generalActivity/queryUserGoldRichSum?app=com.mipay.wallet&deviceType=2&system=1&visitEnvironment=2&userExtra={"platformType":1,"com.miui.player":"********","com.miui.video":"v2024090290(MiVideo-UN)","com.mipay.wallet":"6.83.0.5175.2256"}&activityCode=2211-videoWelfare')
        
        if total_res and total_res.get('code') == 0:
            exchanger.total_days = int(total_res['value']) / 100
            print(f"💎 总天数: {exchanger.total_days}")
        else:
            print(f"⚠️ 天数查询失败: {total_res}")
            exchanger.total_days = 0.0
        
        # 跳过兑换历史检查，假设从未兑换
        exchanger.has_exchanged_before = False
        print(f"📜 兑换历史: 跳过检查，假设从未兑换")
        
    except Exception as e:
        print(f"❌ 用户信息查询异常: {str(e)}")
        return
    
    # 直接测试兑换
    print("\n🎬 步骤4: 直接测试兑换")
    test_phone = "18348185287"  # 指定的测试手机号
    exchange_type = "tencent"   # 腾讯会员
    
    print(f"📱 兑换手机号: {test_phone}")
    print(f"🎬 兑换类型: {exchange_type} (腾讯视频VIP月卡)")
    print(f"💎 当前天数: {exchanger.total_days}")
    print(f"🔧 使用convertGoldRich接口")
    
    print("\n🚀 开始兑换...")
    
    success, message, raw_response = exchanger.exchange_member(test_phone, exchange_type)
    
    print(f"\n📋 兑换结果:")
    print(f"✅ 成功: {success}")
    print(f"💬 消息: {message}")
    print(f"🔧 原始响应:")
    print("-" * 50)
    import json
    print(json.dumps(raw_response, indent=2, ensure_ascii=False))
    print("-" * 50)
    
    # 分析响应
    if success:
        print("\n🎉 兑换成功分析:")
        if 'value' in raw_response and 'prizeInfo' in raw_response['value']:
            prize_info = raw_response['value']['prizeInfo']
            print(f"🎁 奖品描述: {prize_info.get('prizeDesc', '未知')}")
            print(f"🎬 兑换描述: {prize_info.get('prizeGiveDesc2', '未知')}")
            print(f"🕐 使用时间: {prize_info.get('useTime', '未知')}")
            print(f"💰 消耗天数: {raw_response['value'].get('value', 0) / -100}")
            print(f"💎 剩余天数: {raw_response['value'].get('afterChangeValue', 0) / 100}")
    else:
        print(f"\n❌ 兑换失败分析:")
        if 'error' in raw_response:
            print(f"🔍 错误信息: {raw_response['error']}")
        if 'code' in raw_response:
            print(f"🔍 错误代码: {raw_response['code']}")
    
    return success, message, raw_response

if __name__ == "__main__":
    test_convert_exchange()
