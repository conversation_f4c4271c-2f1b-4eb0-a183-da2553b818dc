#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小米钱包兑换会员专用脚本
功能：专门用于兑换会员，支持批量兑换和条件检查
"""

import os
import sys
import time
import requests
import urllib3
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# ==================== 兑换功能设置 ====================
# 尝试从配置文件读取设置，如果失败则使用默认值
try:
    from 兑换配置 import (
        EXCHANGE_TYPE, MIN_DAYS_REQUIRED, EXCHANGE_HOUR,
        FORCE_EXCHANGE, IGNORE_EXCHANGE_HISTORY,
        EXCHANGE_INTERVAL, MAX_RETRY_COUNT
    )
    print("✅ 已加载兑换配置文件")
except ImportError:
    # 默认配置
    EXCHANGE_TYPE = "iqiyi"
    MIN_DAYS_REQUIRED = 7
    EXCHANGE_HOUR = 10
    FORCE_EXCHANGE = False
    IGNORE_EXCHANGE_HISTORY = False
    EXCHANGE_INTERVAL = 2
    MAX_RETRY_COUNT = 3
    print("⚠️ 未找到配置文件，使用默认配置")

# =======================================================

class RnlRequest:
    def __init__(self, cookies: Union[str, dict]):
        self.session = requests.Session()
        self._base_headers = {
            'Host': 'm.jr.airstarfinance.net',
            'User-Agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2012K11AC Build/UKQ1.230804.001; AppBundle/com.mipay.wallet; AppVersionName/6.89.1.5275.2323; AppVersionCode/20577595; MiuiVersion/stable-V816.0.13.0.UMNCNXM; DeviceId/alioth; NetworkType/WIFI; mix_version; WebViewVersion/*********) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        }
        self.update_cookies(cookies)

    def request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Union[Dict[str, Any], str, bytes]] = None,
        json: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        headers = {**self._base_headers, **kwargs.pop('headers', {})}
        try:
            resp = self.session.request(
                verify=False,
                method=method.upper(),
                url=url,
                params=params,
                data=data,
                json=json,
                headers=headers,
                **kwargs
            )
            resp.raise_for_status()
            return resp.json()
        except:
            return None

    def update_cookies(self, cookies: Union[str, dict]) -> None:
        if cookies:
            if isinstance(cookies, str):
                dict_cookies = self._parse_cookies(cookies)
            else:
                dict_cookies = cookies
            self.session.cookies.update(dict_cookies)
            self._base_headers['Cookie'] = self.dict_cookie_to_string(dict_cookies)

    @staticmethod
    def _parse_cookies(cookies_str: str) -> Dict[str, str]:
        return dict(
            item.strip().split('=', 1)
            for item in cookies_str.split(';')
            if '=' in item
        )

    @staticmethod
    def dict_cookie_to_string(cookie_dict):
        cookie_list = []
        for key, value in cookie_dict.items():
            cookie_list.append(f"{key}={value}")
        return "; ".join(cookie_list)

    def get(self, url: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[Dict[str, Any]]:
        return self.request('GET', url, params=params, **kwargs)

    def post(self, url: str, data: Optional[Union[Dict[str, Any], str, bytes]] = None,
             json: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[Dict[str, Any]]:
        return self.request('POST', url, data=data, json=json, **kwargs)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.session.close()


class XiaomiExchanger:
    def __init__(self, cookies):
        self.activity_code = '2211-videoWelfare'
        self.rr = RnlRequest(cookies)
        self.total_days = 0.0
        self.has_exchanged_before = False

    def query_user_info(self):
        """查询用户总天数"""
        try:
            # 查询总天数
            total_res = self.rr.get('https://m.jr.airstarfinance.net/mp/api/generalActivity/queryUserGoldRichSum?app=com.mipay.wallet&deviceType=2&system=1&visitEnvironment=2&userExtra={"platformType":1,"com.miui.player":"********","com.miui.video":"v2024090290(MiVideo-UN)","com.mipay.wallet":"6.83.0.5175.2256"}&activityCode=2211-videoWelfare')
            if total_res and total_res['code'] == 0:
                self.total_days = int(total_res['value']) / 100
            
            # 检查是否曾经兑换过
            self.has_exchanged_before = self.check_exchange_history()
            
            return True
        except:
            return False

    def check_exchange_history(self):
        """检查兑换历史记录，判断是否曾经兑换过"""
        try:
            # 查询兑换记录
            history_res = self.rr.get(
                f'https://m.jr.airstarfinance.net/mp/api/generalActivity/queryUserExchangeList?activityCode={self.activity_code}&pageNum=1&pageSize=20',
            )
            
            if history_res and history_res['code'] == 0:
                # 如果有兑换记录，则说明曾经兑换过
                return len(history_res['value']['data']) > 0
        except:
            pass
        return False

    def exchange_member(self, phone: str) -> tuple[bool, str]:
        """兑换会员"""
        try:
            # 兑换请求
            url = f"https://m.jr.airstarfinance.net/mp/api/generalActivity/exchange?activityCode={self.activity_code}&exchangeCode={EXCHANGE_TYPE}&phone={phone}&app=com.mipay.wallet&deviceType=2&system=1&visitEnvironment=2&userExtra=%7B%22platformType%22:1%7D"
            response = self.rr.get(url)
            
            if response:
                if response.get('code') == 0:
                    return True, "兑换成功"
                else:
                    # 如果返回了错误消息，显示具体错误
                    error_msg = response.get('message', '缺货补货中')
                    return False, f"兑换失败: {error_msg}"
            else:
                return False, "兑换失败: 缺货补货中，明天再试"
        except Exception as e:
            return False, f"兑换请求异常: {str(e)}"


def get_xiaomi_cookies(pass_token, user_id):
    """获取小米钱包cookies"""
    login_url = 'https://account.xiaomi.com/pass/serviceLogin?callback=https%3A%2F%2Fapi.jr.airstarfinance.net%2Fsts%3Fsign%3D1dbHuyAmee0NAZ2xsRw5vhdVQQ8%253D%26followup%3Dhttps%253A%252F%252Fm.jr.airstarfinance.net%252Fmp%252Fapi%252Flogin%253Ffrom%253Dmipay_indexicon_TVcard%2526deepLinkEnable%253Dfalse%2526requestUrl%253Dhttps%25253A%25252F%25252Fm.jr.airstarfinance.net%25252Fmp%25252Factivity%25252FvideoActivity%25253Ffrom%25253Dmipay_indexicon_TVcard%252526_noDarkMode%25253Dtrue%252526_transparentNaviBar%25253Dtrue%252526cUserId%25253Dusyxgr5xjumiQLUoAKTOgvi858Q%252526_statusBarHeight%25253D137&sid=jrairstar&_group=DEFAULT&_snsNone=true&_loginType=ticket'
    headers = {
        'user-agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2012K11AC Build/UKQ1.230804.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/100.0.4896.127 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'cookie': f'passToken={pass_token}; userId={user_id};'
    }

    try:
        session = requests.Session()
        session.get(url=login_url, headers=headers, verify=False, timeout=10)
        cookies = session.cookies.get_dict()
        if 'cUserId' in cookies and 'serviceToken' in cookies:
            return f"cUserId={cookies.get('cUserId')};jrairstar_serviceToken={cookies.get('serviceToken')}"
    except:
        pass
    return None


def load_accounts_from_file():
    """从ck.txt文件读取账号信息"""
    accounts = []
    
    try:
        with open('ck.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            # 解析格式：手机号----cookies----其他信息----加密信息----passToken
            parts = line.split('----')
            if len(parts) < 5:
                print(f"⚠️ 第{line_num}行格式错误，跳过")
                continue
            
            phone = parts[0].strip()
            cookies = parts[1].strip()
            pass_token_section = parts[4].strip()
            
            # 从passToken部分提取passToken和userId
            pass_token = ""
            user_id = ""
            
            # passToken部分格式：passToken=xxx; userId=xxx; psecurity=xxx
            token_parts = pass_token_section.split(';')
            for token_part in token_parts:
                token_part = token_part.strip()
                if token_part.startswith('passToken='):
                    pass_token = token_part.split('passToken=')[1].strip()
                elif token_part.startswith('userId='):
                    user_id = token_part.split('userId=')[1].strip()
            
            if not user_id:
                print(f"⚠️ 第{line_num}行无法提取userId，跳过")
                continue
            
            accounts.append({
                'passToken': pass_token,
                'userId': user_id,
                'phone': phone
            })
            
        return accounts
        
    except FileNotFoundError:
        print("❌ 未找到ck.txt文件")
        return []
    except Exception as e:
        print(f"❌ 读取ck.txt文件失败: {str(e)}")
        return []


def mask_user_id(user_id):
    """格式化用户ID显示，只显示前三位和后三位，中间用星号代替"""
    if len(user_id) <= 6:
        return '*' * len(user_id)
    return user_id[:3] + '*' * 6 + user_id[-3:]


def format_days(days):
    """格式化天数显示（保留一位小数）"""
    return f"{days:.1f}天"


def check_prize_stock():
    """检查奖品库存状态"""
    url = "https://m.jr.airstarfinance.net/mp/api/generalActivity/getPrizeStatusV2"
    params = {
        "activityCode": "2211-videoWelfare",
        "needPrizeBrand": "youku,mgtv,iqiyi,tencent,bilibili,other"
    }
    headers = {
        "User-Agent": "Mozilla/5.0 (Linux; Android 13) AppleWebKit/537.36"
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        prizes = response.json().get("value", [])

        # 会员类型映射
        type_mapping = {
            "iqiyi": "爱奇艺黄金会员月卡",
            "tencent": "腾讯视频VIP月卡",
            "youku": "优酷VIP会员月卡",
            "mango": "芒果TV会员月卡"
        }

        target_name = type_mapping.get(EXCHANGE_TYPE, "")

        for prize in prizes:
            name = prize.get("prizeName", "").replace("\n", " ").strip()
            if name == target_name:
                status = prize.get("todayStockStatus", 2)
                return status == 1, name

        return False, target_name

    except Exception as e:
        print(f"⚠️ 查询库存失败: {e}")
        return None, ""


def display_all_stock():
    """显示所有会员类型的库存状态"""
    TARGET_PRIZES = [
        "腾讯视频VIP月卡",
        "优酷VIP会员月卡",
        "爱奇艺黄金会员月卡",
        "芒果TV会员月卡",
        "哔哩哔哩会员月卡"
    ]

    url = "https://m.jr.airstarfinance.net/mp/api/generalActivity/getPrizeStatusV2"
    params = {
        "activityCode": "2211-videoWelfare",
        "needPrizeBrand": "youku,mgtv,iqiyi,tencent,bilibili,other"
    }
    headers = {
        "User-Agent": "Mozilla/5.0 (Linux; Android 13) AppleWebKit/537.36"
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        prizes = response.json().get("value", [])

        print("\n📦 当前库存状态:")
        print("-" * 40)

        for target in TARGET_PRIZES:
            found = False
            for prize in prizes:
                name = prize.get("prizeName", "").replace("\n", " ").strip()
                if name == target:
                    status = prize.get("todayStockStatus", 2)
                    status_text = "🟢 有货" if status == 1 else "🔴 无货"
                    print(f"{name}: {status_text}")
                    found = True
                    break
            if not found:
                print(f"{target}: ❓ 未知")

        print("-" * 40)

    except Exception as e:
        print(f"⚠️ 查询库存失败: {e}")


def get_phone_input():
    """获取用户输入的手机号"""
    while True:
        phone = input("请输入要兑换的手机号（输入 'q' 退出）: ").strip()
        if phone.lower() == 'q':
            return None
        if len(phone) == 11 and phone.isdigit():
            return phone
        print("❌ 手机号格式错误，请输入11位数字")

def main():
    print("🎁 小米钱包会员兑换专用脚本")
    print("=" * 50)

    # 从ck.txt文件读取账号信息
    accounts = load_accounts_from_file()

    if not accounts:
        print("❌ 未找到有效账号信息")
        print("请确保ck.txt文件存在且格式正确")
        sys.exit(1)

    # 获取用户输入的手机号
    target_phone = get_phone_input()
    if not target_phone:
        print("❌ 已退出")
        sys.exit(0)
    
    print(f"✅ 找到 {len(accounts)} 个账号")
    print(f"🎯 兑换会员类型: {EXCHANGE_TYPE}")
    print(f"📋 最少天数要求: {MIN_DAYS_REQUIRED}天")
    print(f"⏰ 兑换时间要求: {'任何时间' if EXCHANGE_HOUR == -1 else f'{EXCHANGE_HOUR}点'}")
    print(f"🔧 强制兑换模式: {'已开启' if FORCE_EXCHANGE else '已关闭'}")
    print(f"📜 忽略兑换历史: {'是' if IGNORE_EXCHANGE_HISTORY else '否'}")
    print(f"🔄 最大重试次数: {MAX_RETRY_COUNT}次")
    print("=" * 50)

    # 显示库存状态
    display_all_stock()

    # 检查当前兑换类型的库存
    has_stock, prize_name = check_prize_stock()
    if has_stock is None:
        print("⚠️ 无法查询库存状态，继续兑换可能失败")
    elif not has_stock:
        print(f"❌ {prize_name} 当前无货，建议稍后再试")
        if input("是否仍要继续兑换？(y/N): ").lower() != 'y':
            sys.exit(0)
    else:
        print(f"✅ {prize_name} 当前有货")

    # 获取当前时间（UTC+8）
    beijing_time = datetime.utcnow() + timedelta(hours=8)
    current_hour = beijing_time.hour

    print(f"🕐 当前时间: {beijing_time.strftime('%Y-%m-%d %H:%M:%S')}")

    if not FORCE_EXCHANGE and EXCHANGE_HOUR != -1 and current_hour != EXCHANGE_HOUR:
        print(f"⚠️ 当前时间不是兑换时间（{EXCHANGE_HOUR}点），如需强制兑换请设置 FORCE_EXCHANGE = True")
        if input("是否继续？(y/N): ").lower() != 'y':
            sys.exit(0)
    
    # 执行兑换
    success_count = 0
    failed_count = 0
    
    for idx, account in enumerate(accounts):
        user_id = account.get('userId', '未知')
        phone = account.get('phone', '未知')
        masked_id = mask_user_id(user_id)
        
        print(f"\n▶️ 处理账号 {idx+1}/{len(accounts)} (ID: {masked_id}, 手机: {phone})")
        
        # 获取 cookies
        cookies = get_xiaomi_cookies(
            account.get('passToken', ''), 
            account.get('userId', '')
        )
        
        if not cookies:
            print("❌ 登录失败，跳过")
            failed_count += 1
            continue
        
        try:
            exchanger = XiaomiExchanger(cookies)
            
            # 查询用户信息
            if not exchanger.query_user_info():
                print("❌ 无法查询账户信息，跳过")
                failed_count += 1
                continue
            
            print(f"💎 当前总天数: {format_days(exchanger.total_days)}")
            print(f"📜 兑换历史: {'已兑换过' if exchanger.has_exchanged_before else '从未兑换'}")
            
            # 检查兑换条件
            can_exchange = True
            reasons = []

            if not FORCE_EXCHANGE:
                if exchanger.total_days < MIN_DAYS_REQUIRED:
                    can_exchange = False
                    reasons.append(f"总天数不足{MIN_DAYS_REQUIRED}天（当前{exchanger.total_days:.1f}天）")

                if exchanger.has_exchanged_before and not IGNORE_EXCHANGE_HISTORY:
                    can_exchange = False
                    reasons.append("该账号已兑换过会员")

                if EXCHANGE_HOUR != -1 and current_hour != EXCHANGE_HOUR:
                    can_exchange = False
                    reasons.append(f"当前时间 {beijing_time.strftime('%H:%M')} 非{EXCHANGE_HOUR}点")
            
            if not can_exchange:
                print(f"⚠️ 不满足兑换条件: {'，'.join(reasons)}")
                continue
            
            # 执行兑换（支持重试）
            print(f"🎁 开始兑换{EXCHANGE_TYPE}会员到手机号: {target_phone}...")

            retry_count = 0
            success = False
            message = ""

            while retry_count < MAX_RETRY_COUNT and not success:
                if retry_count > 0:
                    print(f"🔄 第{retry_count + 1}次尝试...")

                success, message = exchanger.exchange_member(target_phone)

                if not success and retry_count < MAX_RETRY_COUNT - 1:
                    print(f"⚠️ {message}，{EXCHANGE_INTERVAL}秒后重试...")
                    time.sleep(EXCHANGE_INTERVAL)

                retry_count += 1

            if success:
                print(f"🎉 {message}")
                success_count += 1
            else:
                print(f"❌ {message}")
                failed_count += 1

        except Exception as e:
            print(f"⚠️ 处理异常: {str(e)}")
            failed_count += 1

        time.sleep(EXCHANGE_INTERVAL)  # 避免请求过快
    
    # 最终统计
    print("\n" + "=" * 50)
    print(f"🎁 兑换完成")
    print(f"✅ 成功兑换: {success_count}个")
    print(f"❌ 兑换失败: {failed_count}个")
    print(f"📊 总计处理: {len(accounts)}个账号")
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)


if __name__ == "__main__":
    main()
