#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 测试Web兑换接口
url = "http://127.0.0.1:5000/api/exchange"
data = {
    "phone": "18348185287",
    "userId": "3057699427",  # 正确的用户ID
    "exchangeType": "tencent"
}

print("发送兑换请求...")
print(f"URL: {url}")
print(f"数据: {json.dumps(data, indent=2)}")

try:
    response = requests.post(url, json=data, timeout=30)
    print(f"\n状态码: {response.status_code}")
    print(f"响应: {response.text}")
except Exception as e:
    print(f"错误: {e}")
