<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兑换成功效果展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .success-message {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            animation: slideInDown 0.5s ease-out;
        }
        
        .success-details {
            background: #e8f5e9;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            animation: fadeInUp 0.8s ease-out;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #c8e6c9;
            font-size: 16px;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: bold;
            color: #2e7d32;
            display: flex;
            align-items: center;
        }
        
        .detail-value {
            color: #1b5e20;
            font-weight: 600;
        }
        
        .icon {
            margin-right: 8px;
            font-size: 18px;
        }
        
        .exchange-record {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            animation: bounceIn 1s ease-out;
        }
        
        .record-title {
            font-size: 18px;
            font-weight: bold;
            color: #856404;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .record-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .record-info {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .record-badge {
            background: #4CAF50;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        
        .celebration {
            text-align: center;
            margin: 20px 0;
            font-size: 48px;
            animation: bounce 2s infinite;
        }
        
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            animation: slideInRight 0.5s ease-out;
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .record-info {
                flex-direction: column;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部通知 -->
    <div class="notification" id="notification">
        🎉 兑换成功！会员已充值到指定手机号
    </div>

    <div class="container">
        <div class="header">
            <h1>🎁 小米钱包会员兑换系统</h1>
            <p>兑换成功展示效果</p>
        </div>
        
        <div class="main-content">
            <!-- 庆祝动画 -->
            <div class="celebration">🎉🎊✨</div>
            
            <!-- 成功消息 -->
            <div class="success-message">
                ✅ 兑换成功！爱奇艺黄金会员月卡已充值完成
            </div>
            
            <!-- 兑换详情 -->
            <div class="success-details">
                <div class="detail-item">
                    <span class="detail-label">
                        <span class="icon">📱</span>
                        充值手机号
                    </span>
                    <span class="detail-value">19202517653</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">
                        <span class="icon">🎬</span>
                        会员类型
                    </span>
                    <span class="detail-value">爱奇艺黄金会员月卡</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">
                        <span class="icon">🕐</span>
                        兑换时间
                    </span>
                    <span class="detail-value">2025-07-03 02:35:42</span>
                </div>
                

                
                <div class="detail-item">
                    <span class="detail-label">
                        <span class="icon">✅</span>
                        兑换状态
                    </span>
                    <span class="detail-value">成功</span>
                </div>
            </div>
            
            <!-- 兑换记录 -->
            <div class="exchange-record">
                <div class="record-title">📋 最新兑换记录</div>
                
                <div class="record-item">
                    <div class="record-info">
                        <span><strong>📱 19202517653</strong></span>
                        <span>🎬 爱奇艺</span>
                        <span>🕐 2025-07-03 02:35:42</span>
                    </div>
                    <span class="record-badge">✅ 成功</span>
                </div>

                <div class="record-item">
                    <div class="record-info">
                        <span><strong>📱 19202514279</strong></span>
                        <span>🎬 腾讯视频</span>
                        <span>🕐 2025-07-03 01:28:15</span>
                    </div>
                    <span class="record-badge">✅ 成功</span>
                </div>

                <div class="record-item">
                    <div class="record-info">
                        <span><strong>📱 19212425803</strong></span>
                        <span>🎬 优酷</span>
                        <span>🕐 2025-07-02 23:45:33</span>
                    </div>
                    <span class="record-badge">✅ 成功</span>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn" onclick="showSuccess()">🎁 模拟兑换成功</button>
                <button class="btn btn-secondary" onclick="location.reload()">🔄 重新演示</button>
                <button class="btn btn-secondary" onclick="window.close()">❌ 关闭页面</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟兑换成功效果
        function showSuccess() {
            // 显示通知
            const notification = document.getElementById('notification');
            notification.style.display = 'block';
            
            // 3秒后隐藏通知
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.style.display = 'none';
                    notification.style.opacity = '1';
                }, 500);
            }, 3000);
            
            // 播放成功音效（如果浏览器支持）
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.play();
            } catch (e) {
                console.log('音效播放失败');
            }
        }
        
        // 页面加载时自动显示成功效果
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                showSuccess();
            }, 1000);
        });
        
        // 添加一些交互效果
        document.querySelectorAll('.detail-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#c8e6c9';
                this.style.transform = 'translateX(5px)';
                this.style.transition = 'all 0.3s ease';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.backgroundColor = 'transparent';
                this.style.transform = 'translateX(0)';
            });
        });
        
        // 记录项悬停效果
        document.querySelectorAll('.record-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
                this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                this.style.transition = 'all 0.3s ease';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
</body>
</html>
