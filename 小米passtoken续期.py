import requests
import urllib3
import time
import os
import json
import re
import uuid
import hashlib
import base64
from urllib.parse import quote

# 导入账号密码登录相关函数
from 小米账号密码登录获取cookie import get_xiaomi_cookie, md5_encrypt, parse_login_response, generate_signed_url, read_accounts

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def update_cookies_file(phone, cookie_data, file_path='ck.txt'):
    """更新cookies文件中指定手机号的cookie信息，严格保持原始格式，只更新值"""
    try:
        # 读取原文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 更新文件内容
        updated = False
        for i, line in enumerate(lines):
            if line.strip() and line.startswith(phone):
                # 获取原始行
                original_line = line.strip()
                
                # 分解原始行
                parts = original_line.split('----')
                if len(parts) < 4:
                    print(f"账号 {phone} 的格式不正确，应该包含至少4个部分")
                    continue
                
                # 更新第二部分 - 第一组cookies
                if cookie_data.get('cUserId') and cookie_data.get('jrairstar_serviceToken'):
                    parts[1] = f"cUserId={cookie_data['cUserId']}; jrairstar_serviceToken={cookie_data['jrairstar_serviceToken']}; jrairstar_slh={cookie_data.get('jrairstar_slh', '')}; jrairstar_ph={cookie_data.get('jrairstar_ph', '')}"
                
                # 第三部分保持不变，只更新jrairstar_ph参数值
                if cookie_data.get('third_part_ph') and "jrairstar_ph=" in parts[2]:
                    # 关键：保持第三部分的原始格式，只替换jrairstar_ph的值
                    third_part_items = []
                    for item in parts[2].split('&'):
                        if item.startswith('jrairstar_ph='):
                            third_part_items.append(f"jrairstar_ph={cookie_data['third_part_ph']}")
                        else:
                            third_part_items.append(item)
                    parts[2] = '&'.join(third_part_items)
                
                # 第四部分：如果有至少5个部分，就保持第四部分不变
                # 第五部分：如果有第五部分，则保持不变；否则从第四部分中尝试提取token部分
                
                # 重建更新后的行
                if len(parts) >= 5:
                    updated_line = '----'.join(parts)
                elif len(parts) == 4:
                    # 检查第四部分是否包含passToken，如果包含，则需要拆分为第四和第五部分
                    if "passToken=" in parts[3]:
                        # 假设第四部分可能是混合了jrairstar_ph和passToken等信息
                        fourth_part = parts[3].split("----")[0] if "----" in parts[3] else parts[3]
                        fifth_part = ""
                        if "passToken=" in parts[3]:
                            # 提取passToken部分
                            token_items = parts[3].split("; ")
                            token_parts = []
                            for item in token_items:
                                if item.startswith("passToken=") or item.startswith("userId=") or item.startswith("psecurity="):
                                    token_parts.append(item)
                            fifth_part = "; ".join(token_parts)
                        
                        if fifth_part:
                            # 使用原始第四部分的jrairstar_ph值(如果存在)
                            fourth_part = cookie_data.get('jrairstar_ph', parts[3].split("----")[0] if "----" in parts[3] else "")
                            parts = parts[:3] + [fourth_part, fifth_part]
                            updated_line = '----'.join(parts)
                        else:
                            # 无法提取token部分，保持原样
                            updated_line = original_line
                    else:
                        # 第四部分不包含passToken，使用原始格式
                        updated_line = original_line
                else:
                    # 格式不正确，保持原样
                    updated_line = original_line
                
                # 如果有更新，写入新行
                if updated_line != original_line:
                    lines[i] = updated_line + '\n'
                    updated = True
                    print(f"已更新账号 {phone} 的cookies")
                    print(f"原始行: {original_line}")
                    print(f"更新后: {updated_line}")
                    break
        
        # 写回文件
        if updated:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            return True
        else:
            print(f"未找到账号 {phone} 的记录或没有需要更新的内容")
            return False
            
    except Exception as e:
        print(f"更新cookies文件出错: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

# 添加读取账号密码文件函数
def get_password_for_phone(phone, account_file='小米账号密码.txt'):
    """从账号密码文件中获取指定手机号的密码"""
    try:
        with open(account_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '----' in line:
                    p, pwd = line.split('----', 1)
                    if p == phone:
                        return pwd
        print(f"未在账号文件中找到 {phone} 的密码")
    except Exception as e:
        print(f"读取账号密码文件失败: {str(e)}")
    return None

# 添加保存登录cookie的函数
def save_login_cookie_to_file(phone, login_data, file_path='小米视频cookies1.txt'):
    """将登录获取到的cookie保存到文件"""
    try:
        if not login_data:
            print(f"登录数据无效，无法保存cookie")
            return False
            
        # 准备格式化的cookie字符串
        formatted_cookie = None
        # 确保tokens在所有代码路径中都可访问
        tokens = login_data.get('tokens', {})
        
        # 如果已经有格式化的cookie
        if 'formatted_cookie' in login_data and login_data['formatted_cookie']:
            formatted_cookie = login_data['formatted_cookie']
        else:
            # 从login_data构造格式化的cookie
            cookies = login_data.get('cookies', {})
            
            if cookies and tokens:
                # 必要的参数
                cUserId = cookies.get('cUserId', '')
                serviceToken = tokens.get('serviceToken', '')
                jrairstar_slh = cookies.get('jrairstar_slh', '')
                jrairstar_ph = cookies.get('jrairstar_ph', '')
                
                # musicVersion等参数
                uuid_value = tokens.get('jrairstar_uuid', str(uuid.uuid4()))
                params_str = f"musicVersion=4.32.0.2&session_id=bf94b1dd-20de-4a50-9526-c53aa90dae331744646885463&jrairstar_ph={uuid_value}"
                
                # passToken部分 - 第五部分
                pass_token = tokens.get('passToken', '')
                user_id = tokens.get('userId', '')
                psecurity = tokens.get('psecurity', '')
                
                if not pass_token or not user_id:
                    print(f"❌ 缺少必要的token参数，无法构造有效的cookie")
                    return False
                
                # 生成标准的五部分格式cookie
                # 第一部分：手机号
                # 第二部分：cookie信息
                cookie_str = f"cUserId={cUserId}; jrairstar_serviceToken={serviceToken}; jrairstar_slh={jrairstar_slh}; jrairstar_ph={jrairstar_ph}"
                # 第三部分：params参数
                # 第四部分：jrairstar_ph值（单独作为一部分）
                # 第五部分：passToken信息
                token_str = f"passToken={pass_token}; userId={user_id}; psecurity={psecurity}"
                formatted_cookie = f"{phone}----{cookie_str}----{params_str}----{jrairstar_ph}----{token_str}"
                
                print(f"生成的cookie格式: {phone}----[cookie部分]----[params部分]----[jrairstar_ph]----[token部分]")
                print(f"\n格式化后的Cookie信息:")
                print(formatted_cookie)
                print(f"\n🔍 准备返回的token信息:")
                print(f"passToken: {pass_token}")
                print(f"userId: {user_id}")
                print(f"psecurity: {psecurity}")
        
        if not formatted_cookie:
            print(f"无法格式化cookie数据")
            return False
            
        # 读取现有的cookies
        existing_cookies = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_cookies = [line.strip() for line in f if line.strip()]
        except:
            # 如果文件不存在，就创建一个空列表
            pass
            
        # 确保formatted_cookie是五部分格式
        parts = formatted_cookie.split("----")
        if len(parts) < 5:  # 没有五部分，需要补全
            print(f"⚠️ 需要调整cookie格式为五部分")
            # 确保至少有四部分才能处理
            if len(parts) >= 4:
                # 检查第四部分是否混合了jrairstar_ph和passToken
                fourth_part = parts[3]
                jrairstar_ph = ""
                passtoken_part = ""
                
                # 尝试从第四部分中提取jrairstar_ph和passToken
                if "jrairstar_ph=" in fourth_part and "passToken=" in fourth_part:
                    # 第四部分包含了混合信息，需要拆分
                    for segment in fourth_part.split("; "):
                        if segment.startswith("passToken=") or segment.startswith("userId=") or segment.startswith("psecurity="):
                            if passtoken_part:
                                passtoken_part += "; "
                            passtoken_part += segment
                        elif segment.startswith("jrairstar_ph="):
                            jrairstar_ph = segment.split("=")[1]
                    
                    # 如果成功提取了两部分信息
                    if jrairstar_ph and passtoken_part:
                        parts = parts[:3] + [jrairstar_ph, passtoken_part]
                        formatted_cookie = '----'.join(parts)
                        print(f"✅ 成功调整为五部分格式: {formatted_cookie[:100]}...")
                    else:
                        print(f"⚠️ 无法从第四部分提取passToken信息")
                elif "passToken=" in fourth_part:
                    # 第四部分只包含passToken，需要添加jrairstar_ph部分
                    jrairstar_ph = parts[2].split("jrairstar_ph=")[-1].split("&")[0] if "jrairstar_ph=" in parts[2] else ""
                    parts = parts[:3] + [jrairstar_ph, fourth_part]
                    formatted_cookie = '----'.join(parts)
                    print(f"✅ 成功调整为五部分格式: {formatted_cookie[:100]}...")
                else:
                    # 第四部分可能只是jrairstar_ph
                    # 使用前面已经获取的tokens变量
                    passtoken_part = tokens.get('passToken', '')
                    user_id = tokens.get('userId', '')
                    psecurity = tokens.get('psecurity', '')
                    if passtoken_part and user_id:
                        token_str = f"passToken={passtoken_part}; userId={user_id}; psecurity={psecurity}"
                        formatted_cookie = f"{formatted_cookie}----{token_str}"
                        print(f"✅ 成功添加第五部分: {formatted_cookie[:100]}...")
                    else:
                        print(f"⚠️ 缺少passToken信息，无法添加第五部分")
            else:
                print(f"⚠️ Cookie格式异常，缺少必要部分")
        else:
            print(f"✅ Cookie已经是五部分格式")
        
        # 更新或添加新的cookie
        updated = False
        for i, cookie in enumerate(existing_cookies):
            if cookie.startswith(f"{phone}----"):
                existing_cookies[i] = formatted_cookie
                updated = True
                break
                
        if not updated:
            existing_cookies.append(formatted_cookie)
            
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(existing_cookies))
            
        print(f"✅ 已通过登录方式更新账号 {phone} 的cookies")
        return True
    except Exception as e:
        print(f"❌ 保存cookie时出错: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

def process_account(account_info):
    session = requests.session()
    # 解析账号信息
    print(f"\n===== 开始解析账号信息 =====")
    print(f"原始账号信息: {account_info}")
    
    parts = account_info.strip().split('----')
    print(f"分割后部分数量: {len(parts)}")
    
    if len(parts) < 4:
        print(f"❌ 账号信息格式不正确: {account_info}")
        print(f"❌ 分割后的部分: {parts}")
        return
    
    phone = parts[0]
    print(f"手机号: {phone}")
    
    # 从第二部分获取cUserId等参数
    second_part = parts[1]
    print(f"第二部分(cookies): {second_part}")
    
    try:
        second_part_dict = dict(item.split("=", 1) for item in second_part.split("; "))
        print(f"第二部分解析结果: {second_part_dict}")
        
        cUserId = second_part_dict.get("cUserId", "")
        orig_jrairstar_serviceToken = second_part_dict.get("jrairstar_serviceToken", "")
        orig_jrairstar_slh = second_part_dict.get("jrairstar_slh", "")
        orig_jrairstar_ph = second_part_dict.get("jrairstar_ph", "")
        
        print(f"cUserId: {cUserId}")
        print(f"jrairstar_serviceToken: {orig_jrairstar_serviceToken}")
        print(f"jrairstar_slh: {orig_jrairstar_slh}")
        print(f"jrairstar_ph: {orig_jrairstar_ph}")
    except Exception as e:
        print(f"❌ 解析第二部分时出错: {str(e)}")
        return
    
    # 从第三部分获取jrairstar_ph参数
    third_part = parts[2]
    print(f"第三部分(params): {third_part}")
    
    third_part_jrairstar_ph = ""
    if "jrairstar_ph=" in third_part:
        try:
            temp = third_part.split("jrairstar_ph=")
            if len(temp) > 1:
                third_part_jrairstar_ph = temp[1].split("&")[0] if "&" in temp[1] else temp[1]
            print(f"第三部分jrairstar_ph: {third_part_jrairstar_ph}")
        except Exception as e:
            print(f"❌ 解析第三部分时出错: {str(e)}")
    
    # 从第五部分(或第四部分)获取passToken、userId和psecurity参数
    token_part = ""
    if len(parts) >= 5:  # 五部分格式
        token_part = parts[4]
        print(f"第五部分(token): {token_part}")
    else:  # 四部分格式
        token_part = parts[3]
        print(f"第四部分(可能包含token): {token_part}")
    
    passToken = ""
    userId = ""
    psecurity = ""
    
    try:
        if token_part and "passToken=" in token_part:
            # 按分号分隔各参数
            token_items = token_part.split("; ")
            for item in token_items:
                if "=" in item:
                    key, value = item.split("=", 1)
                    if key == "passToken":
                        passToken = value
                    elif key == "userId":
                        userId = value
                    elif key == "psecurity":
                        psecurity = value
        
        print(f"解析的token信息:")
        print(f"passToken: {passToken[:30]}..." if passToken else "passToken: 未找到")
        print(f"userId: {userId}")
        print(f"psecurity: {psecurity}")
    except Exception as e:
        print(f"❌ 解析token部分时出错: {str(e)}")
        return
    
    if not passToken or not userId:
        print(f"❌ 账号 {phone} 缺少必要的cookie参数")
        # 获取账号密码，尝试重新登录
        password = get_password_for_phone(phone)
        if password:
            print(f"找到账号 {phone} 的密码，尝试重新登录...")
            login_data = get_xiaomi_cookie(phone, password)
            if login_data and 'tokens' in login_data and login_data['tokens'].get('passToken'):
                print("✅ 账号密码登录成功")
                save_login_cookie_to_file(phone, login_data)
                # 递归调用，使用新保存的cookie进行续期
                with open('小米视频cookies1.txt', 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip() and line.startswith(phone):
                            print("使用新登录的cookie重新执行续期")
                            process_account(line.strip())
                            return
        return

    # 恢复续期功能的代码
    cookie = f'passToken={passToken}; userId={userId}; psecurity={psecurity};'
    
    url = 'https://account.xiaomi.com/pass/serviceLogin?callback=https%3A%2F%2Fapi.jr.airstarfinance.net%2Fsts%3Fsign%3D1dbHuyAmee0NAZ2xsRw5vhdVQQ8%253D%26followup%3Dhttps%253A%252F%252Fm.jr.airstarfinance.net%252Fmp%252Fapi%252Flogin%253Ffrom%253Dmipay_indexicon_TVcard%2526deepLinkEnable%253Dfalse%2526requestUrl%253Dhttps%25253A%25252F%25252Fm.jr.airstarfinance.net%25252Fmp%25252Factivity%25252FvideoActivity%25253Ffrom%25253Dmipay_indexicon_TVcard%252526_noDarkMode%25253Dtrue%252526_transparentNaviBar%25253Dtrue%252526cUserId%25253Dusyxgr5xjumiQLUoAKTOgvi858Q%252526_statusBarHeight%25253D137&sid=jrairstar&_group=DEFAULT&_snsNone=true&_loginType=ticket'
    headers = {
        'user-agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2011K2C Build/UKQ1.240624.001; AppBundle/com.mipay.wallet; AppVersionName/6.87.0.5249.2312; AppVersionCode/********; MiuiVersion/stable-OS2.0.2.0.UKBCNXM; DeviceId/venus; NetworkType/MOBILE; mix_version) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'cookie': cookie
    }
    
    try:
        print(f"\n===== 开始续期账号 {phone} =====")
        
        # 步骤1: 登录请求 - 获取基础serviceToken
        print("\n步骤1: 发送登录请求")
        login_response = session.get(url=url, headers=headers, verify=False)
        print(f"登录状态码: {login_response.status_code}")
        print(f"登录响应头: {json.dumps(dict(login_response.headers), indent=2, ensure_ascii=False)}")
        print(f"登录响应内容: {login_response.text[:200]}...") # 只显示前200个字符
        
        # 获取登录后的cookies
        new_cookies = requests.utils.dict_from_cookiejar(session.cookies)
        print(f"登录后Cookies: {new_cookies}")
        
        # 确保获取到serviceToken
        base_serviceToken = new_cookies.get('serviceToken', '')
        if not base_serviceToken:
            print("❌ 未获取到基础serviceToken，尝试使用账号密码登录...")
            # 获取账号密码
            password = get_password_for_phone(phone)
            if not password:
                print(f"❌ 未找到账号 {phone} 的密码，无法使用账号密码登录")
                return
                
            # 使用账号密码登录
            print(f"尝试使用账号 {phone} 密码登录...")
            login_data = get_xiaomi_cookie(phone, password)
            
            if login_data:
                # 检查登录结果
                if 'tokens' in login_data and login_data['tokens'].get('passToken'):
                    print("✅ 账号密码登录成功")
                    # 保存更新后的cookie
                    save_login_cookie_to_file(phone, login_data)
                else:
                    # 检查登录响应中是否有错误信息
                    cookies = login_data.get('cookies', {})
                    if not cookies.get('passToken'):
                        print(f"❌ 登录失败，未获取到passToken")
                        # 尝试获取错误信息
                        if 'login_response' in login_data:
                            response = login_data['login_response']
                            if isinstance(response, dict) and 'desc' in response:
                                print(f"❌ 登录失败原因: {response['desc']}")
            else:
                print("❌ 账号密码登录失败，返回数据为空")
            
            return  # 无论成功与否，都结束当前账号处理
        
        # 步骤2: 米家钱包验证
        print("\n步骤2: 米家钱包验证")
        verify_url = "https://account.xiaomi.com/pass/serviceLogin"
        verify_params = {
            '_json': 'true',
            'appName': 'com.mipay.wallet',
            'sid': 'jrairstar',
            '_locale': 'zh_CN'
        }
        
        verify_response = session.get(
            verify_url,
            params=verify_params,
            headers={
                'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
            },
            verify=False
        )
        
        print(f"验证状态码: {verify_response.status_code}")
        verify_result = parse_login_response(verify_response)
        
        if not verify_result or verify_result.get('code') != 0:
            print(f"❌ 米家钱包验证失败: {verify_result.get('desc', '未知错误') if verify_result else '解析响应失败'}")
            # 即使验证失败，我们也尝试使用基础serviceToken
            final_serviceToken = base_serviceToken
            cookie_data = {
                'cUserId': new_cookies.get('cUserId', cUserId),
                'jrairstar_serviceToken': final_serviceToken,
                'jrairstar_slh': new_cookies.get('jrairstar_slh', orig_jrairstar_slh),
                'jrairstar_ph': new_cookies.get('jrairstar_ph', orig_jrairstar_ph),
                'third_part_ph': third_part_jrairstar_ph
            }
            # 更新cookies文件
            update_cookies_file(phone, cookie_data)
        else:
            # 步骤3: 生成签名URL并获取金融服务令牌
            print("\n步骤3: 生成签名URL并获取金融服务令牌")
            try:
                # 构造需要签名的JSON响应
                response_json = f"&&&START&&&{json.dumps(verify_result)}"
                
                # 生成签名URL
                signed_url = generate_signed_url(response_json)
                if not signed_url:
                    print("❌ 生成签名URL失败，使用基础serviceToken")
                    final_serviceToken = base_serviceToken
                else:
                    print(f"生成的签名URL: {signed_url}")
                    
                    # 使用签名URL获取金融服务令牌
                    jr_sts_response = session.get(
                        signed_url,
                        headers={
                            'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
                        },
                        verify=False
                    )
                    
                    print("\n🔍 金融服务令牌响应详情:")
                    print(f"状态码: {jr_sts_response.status_code}")
                    print(f"Headers: {dict(jr_sts_response.headers)}")
                    print(f"Cookies: {jr_sts_response.cookies.get_dict()}")
                    
                    if jr_sts_response.status_code == 200:
                        print("✅ 成功获取金融服务令牌")
                        
                        # 获取最终的服务令牌
                        final_serviceToken = session.cookies.get_dict().get('serviceToken', base_serviceToken)
                    else:
                        print("❌ 获取金融服务令牌失败，使用基础serviceToken")
                        final_serviceToken = base_serviceToken
                
                # 获取所有需要的cookie信息
                cookies = session.cookies.get_dict()
                new_cUserId = cookies.get('cUserId', new_cookies.get('cUserId', cUserId))
                new_jrairstar_slh = cookies.get('jrairstar_slh', new_cookies.get('jrairstar_slh', orig_jrairstar_slh))
                new_jrairstar_ph = cookies.get('jrairstar_ph', new_cookies.get('jrairstar_ph', orig_jrairstar_ph))
                
                # 准备cookie数据
                cookie_data = {
                    'cUserId': new_cUserId,
                    'jrairstar_serviceToken': final_serviceToken,
                    'jrairstar_slh': new_jrairstar_slh,
                    'jrairstar_ph': new_jrairstar_ph,
                    'third_part_ph': third_part_jrairstar_ph
                }
                
                # 更新cookies文件
                update_cookies_file(phone, cookie_data)
                
            except Exception as e:
                print(f"❌ 获取金融服务令牌异常: {str(e)}")
                # 如果出现异常，使用基础serviceToken
                cookie_data = {
                    'cUserId': new_cookies.get('cUserId', cUserId),
                    'jrairstar_serviceToken': base_serviceToken,
                    'jrairstar_slh': new_cookies.get('jrairstar_slh', orig_jrairstar_slh),
                    'jrairstar_ph': new_cookies.get('jrairstar_ph', orig_jrairstar_ph),
                    'third_part_ph': third_part_jrairstar_ph
                }
                # 更新cookies文件
                update_cookies_file(phone, cookie_data)
        
        # 步骤4: 查询用户金币 (无论前面的步骤成功与否，都尝试查询一下)
        print("\n步骤4: 查询用户金币")
        query_url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/queryUserGoldRichSum?app=com.mipay.wallet&deviceType=2&system=1&visitEnvironment=2&userExtra={"platformType":1,"com.miui.player":"********","com.miui.video":"v2024090290(MiVideo-UN)","com.mipay.wallet":"6.83.0.5175.2256"}&activityCode=2211-videoWelfare'
        query_headers = {
            'user-agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2011K2C Build/UKQ1.240624.001; AppBundle/com.mipay.wallet; AppVersionName/6.87.0.5249.2312; AppVersionCode/********; MiuiVersion/stable-OS2.0.2.0.UKBCNXM; DeviceId/venus; NetworkType/MOBILE; mix_version) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        }
        query_response = session.get(url=query_url, headers=query_headers, verify=False)
        query_text = query_response.text
        print(f"查询状态码: {query_response.status_code}")
        print(f"查询响应头: {json.dumps(dict(query_response.headers), indent=2, ensure_ascii=False)}")
        print(f"查询响应内容: {query_text}")
        
        # 尝试解析JSON响应
        try:
            query_json = json.loads(query_text)
            print(f"查询响应JSON: {json.dumps(query_json, indent=2, ensure_ascii=False)}")
        except:
            print("查询响应不是有效的JSON格式")
            
        print(f"\n账号 {phone} 续期完成")
    except Exception as e:
        print(f"账号 {phone} 处理出错: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

if __name__ == '__main__':
    # 读取cookies文件
    try:
        with open('小米视频cookies1.txt', 'r', encoding='utf-8') as f:
            accounts = f.readlines()
        
        for account in accounts:
            if account.strip():  # 跳过空行
                process_account(account)
                print("\n等待5秒后处理下一个账号...")
                time.sleep(5)
                
    except FileNotFoundError:
        print("未找到小米视频cookies1.txt文件")
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
