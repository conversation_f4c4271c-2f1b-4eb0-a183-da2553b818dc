#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移除兑换历史限制后的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web_server import get_xiaomi_cookies, XiaomiExchanger, load_accounts_from_file, mask_user_id

def test_no_history_limit():
    print("🧪 测试移除兑换历史限制后的功能")
    print("=" * 60)
    
    # 加载账号
    accounts = load_accounts_from_file()
    if not accounts:
        print("❌ 未找到账号信息")
        return
    
    # 测试前几个账号
    test_accounts = accounts[:3]  # 测试前3个账号
    
    for i, account in enumerate(test_accounts, 1):
        print(f"\n📱 测试账号 {i}: {account['phone']}")
        print(f"👤 用户ID: {mask_user_id(account['userId'])}")
        
        # 获取cookies
        print(f"🔐 获取cookies...")
        cookies = get_xiaomi_cookies(account['passToken'], account['userId'])
        
        if not cookies:
            print("❌ cookies获取失败")
            continue
        
        print("✅ cookies获取成功")
        
        # 创建兑换器
        exchanger = XiaomiExchanger(cookies)
        
        # 查询用户信息
        print(f"📊 查询用户信息...")
        if exchanger.query_user_info():
            print(f"💎 总天数: {exchanger.total_days}")
            print(f"📜 兑换历史: {'已兑换过' if exchanger.has_exchanged_before else '从未兑换'}")
            
            # 检查是否可以兑换
            if exchanger.total_days >= 31:
                print(f"✅ 可以兑换（天数: {exchanger.total_days} >= 31）")
                print(f"🎯 不再限制兑换历史，即使之前兑换过也可以继续兑换")
            else:
                print(f"❌ 不能兑换（天数: {exchanger.total_days} < 31）")
        else:
            print("❌ 用户信息查询失败")
        
        print("-" * 40)
    
    print("\n🎯 测试总结:")
    print("✅ 已移除兑换历史限制")
    print("✅ 只要天数 >= 31 就可以兑换")
    print("✅ 之前兑换过的账号也可以继续兑换")
    print("✅ 所有账号都会在Web界面显示")

if __name__ == "__main__":
    test_no_history_limit()
