# 小米钱包会员兑换Web系统使用说明

## 🚀 快速启动

### 1. 启动Web服务器
```bash
python web_server.py
```

### 2. 访问Web界面
打开浏览器访问：`http://127.0.0.1:5000`

## 📋 功能说明

### 1. 实时库存状态
- **功能**：显示各种会员类型的实时库存状态
- **操作**：点击"🔄 刷新库存"按钮更新库存信息
- **显示**：
  - 🟢 有货：当前有库存，可以兑换
  - 🔴 无货：当前无库存，暂时无法兑换

### 2. 会员兑换功能
- **手机号输入**：输入要兑换到的手机号（11位数字）
- **会员类型选择**：
  - 爱奇艺黄金会员月卡
  - 腾讯视频VIP月卡
  - 优酷VIP会员月卡
  - 芒果TV会员月卡
- **兑换流程**：
  1. 输入手机号
  2. 选择会员类型
  3. 选择要使用的账号
  4. 点击"🎁 立即兑换"

### 3. 可用账号管理
- **显示条件**：只显示天数≥31天且未兑换过的账号
- **账号信息**：
  - 用户ID（脱敏显示）
  - 手机号
  - 总天数
  - 兑换状态
  - Token信息（部分显示）
- **操作功能**：
  - 🔄 刷新账号：重新加载账号信息
  - 📊 查询天数：更新所有账号的天数信息
  - 选择账号：点击"选择此账号兑换"按钮

### 4. 兑换记录
- **记录内容**：
  - 兑换手机号
  - 兑换时间
  - 会员类型
  - 使用的账号ID
  - 兑换状态
- **数据存储**：记录保存在浏览器本地存储中
- **管理功能**：可以清空所有兑换记录

## 🎯 使用流程

### 标准兑换流程
1. **检查库存**：首先查看要兑换的会员类型是否有货
2. **输入手机号**：在兑换区域输入目标手机号
3. **选择会员类型**：从下拉菜单选择要兑换的会员类型
4. **选择账号**：在账号管理区域选择一个可用账号
5. **执行兑换**：点击"立即兑换"按钮
6. **查看结果**：系统会显示兑换结果并记录到兑换历史

### 账号筛选规则
- ✅ 总天数 ≥ 31天
- ✅ 从未兑换过会员
- ✅ 账号状态正常（能正常登录）

### 兑换后处理
- 兑换成功的账号会从可用列表中移除
- 可以通过"查询天数"按钮重新检查账号状态
- 如果账号天数重新达到31天以上，会重新显示

## 🔧 配置说明

### 修改最少天数要求
编辑 `兑换配置.py` 文件：
```python
MIN_DAYS_REQUIRED = 31  # 修改为你需要的天数
```

### 修改默认兑换类型
编辑 `兑换配置.py` 文件：
```python
EXCHANGE_TYPE = "iqiyi"  # 修改为默认的兑换类型
```

## 🛡️ 安全特性

### 1. 数据脱敏
- 用户ID只显示前3位和后3位
- Token信息只显示前20个字符
- 保护用户隐私信息

### 2. 输入验证
- 手机号格式验证（11位数字）
- 账号状态验证
- 库存状态检查

### 3. 错误处理
- 网络请求失败处理
- 兑换失败提示
- 用户友好的错误信息

## 📱 响应式设计

- **桌面端**：完整功能展示，多列布局
- **移动端**：自适应单列布局，触摸友好
- **平板端**：中等屏幕优化布局

## 🔍 故障排除

### 1. 页面无法访问
- 检查Web服务器是否正常启动
- 确认端口5000没有被占用
- 检查防火墙设置

### 2. 账号不显示
- 确认ck.txt文件存在且格式正确
- 检查账号天数是否≥31天
- 确认账号未兑换过会员

### 3. 兑换失败
- 检查库存状态（可能无货）
- 确认手机号格式正确
- 检查网络连接状态
- 查看控制台错误信息

### 4. 库存查询失败
- 检查网络连接
- 确认API接口正常
- 稍后重试

## 📊 技术架构

### 后端技术
- **Flask**：Web框架
- **Python**：主要编程语言
- **Requests**：HTTP请求库

### 前端技术
- **HTML5**：页面结构
- **CSS3**：样式设计（渐变、动画、响应式）
- **JavaScript**：交互逻辑
- **LocalStorage**：本地数据存储

### 数据流程
1. 前端发起API请求
2. 后端读取ck.txt文件
3. 验证账号状态和天数
4. 执行兑换操作
5. 返回结果给前端
6. 前端更新界面和记录

## 🎨 界面特色

- **现代化设计**：渐变背景、卡片布局、阴影效果
- **直观操作**：大按钮、清晰标识、状态提示
- **实时反馈**：加载状态、成功提示、错误警告
- **数据可视化**：库存状态、账号信息、兑换记录

## 📝 注意事项

1. **兑换限制**：每个账号只能兑换一次会员
2. **天数要求**：账号天数必须≥31天才能兑换
3. **库存状态**：建议兑换前先检查库存
4. **网络环境**：确保网络连接稳定
5. **数据备份**：兑换记录保存在浏览器本地，清除浏览器数据会丢失记录
