#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试兑换功能 - 腾讯会员
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web_server import get_xiaomi_cookies, XiaomiExchanger, load_accounts_from_file, mask_user_id

def test_exchange():
    print("🧪 开始测试兑换功能 - 腾讯会员")
    print("=" * 50)

    # 加载账号
    accounts = load_accounts_from_file()
    if not accounts:
        print("❌ 未找到账号信息")
        return

    # 使用第一个账号进行测试
    account = accounts[0]
    print(f"📱 测试账号: {account['phone']}")
    print(f"👤 用户ID: {mask_user_id(account['userId'])}")

    # 获取cookies
    print("\n🔐 步骤1: 获取cookies")
    cookies = get_xiaomi_cookies(account['passToken'], account['userId'])

    if not cookies:
        print("❌ cookies获取失败")
        return

    print("✅ cookies获取成功")

    # 创建兑换器
    print("\n🎁 步骤2: 创建兑换器")
    exchanger = XiaomiExchanger(cookies)

    # 查询用户信息
    print("\n📊 步骤3: 查询用户信息")
    if exchanger.query_user_info():
        print(f"💎 总天数: {exchanger.total_days}")
        print(f"📜 兑换历史: {'已兑换过' if exchanger.has_exchanged_before else '从未兑换'}")
    else:
        print("❌ 用户信息查询失败")
        return

    # 测试兑换腾讯会员
    print("\n🎬 步骤4: 测试兑换腾讯会员 (使用新接口)")
    test_phone = "***********"  # 指定的测试手机号
    exchange_type = "tencent"   # 腾讯会员

    print(f"📱 兑换手机号: {test_phone}")
    print(f"🎬 兑换类型: {exchange_type} (腾讯视频VIP月卡)")
    print(f"🔧 使用新的convertGoldRich接口")

    # 检查是否满足兑换条件
    if exchanger.total_days < 31:  # 降低测试门槛
        print(f"⚠️ 天数可能不足（当前{exchanger.total_days}天），但继续测试...")

    if exchanger.has_exchanged_before:
        print("⚠️ 该账号已兑换过会员，但继续测试...")

    print("🚀 开始兑换测试...")

    success, message, raw_response = exchanger.exchange_member(test_phone, exchange_type)

    print(f"\n📋 兑换结果:")
    print(f"✅ 成功: {success}")
    print(f"💬 消息: {message}")
    print(f"🔧 原始响应:")
    print("-" * 40)
    import json
    print(json.dumps(raw_response, indent=2, ensure_ascii=False))
    print("-" * 40)

    return success, message, raw_response

def test_single_account(user_id):
    """测试指定账号"""
    print(f"🧪 测试指定账号: {mask_user_id(user_id)}")
    print("=" * 50)
    
    accounts = load_accounts_from_file()
    account = next((acc for acc in accounts if acc['userId'] == user_id), None)
    
    if not account:
        print("❌ 未找到指定账号")
        return
    
    print(f"📱 账号手机号: {account['phone']}")
    
    # 获取cookies
    cookies = get_xiaomi_cookies(account['passToken'], account['userId'])
    if not cookies:
        print("❌ cookies获取失败")
        return
    
    # 测试兑换
    exchanger = XiaomiExchanger(cookies)
    if not exchanger.query_user_info():
        print("❌ 用户信息查询失败")
        return
    
    print(f"💎 总天数: {exchanger.total_days}")
    
    # 如果天数足够，进行测试兑换
    if exchanger.total_days >= 7:
        test_phone = account['phone']  # 使用真实手机号
        success, message, raw_response = exchanger.exchange_member(test_phone, "iqiyi")
        
        print(f"\n📋 兑换结果:")
        print(f"✅ 成功: {success}")
        print(f"💬 消息: {message}")
        print(f"🔧 原始响应: {raw_response}")
    else:
        print(f"⚠️ 天数不足，无法兑换（需要≥7天，当前{exchanger.total_days}天）")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 测试指定账号
        user_id = sys.argv[1]
        test_single_account(user_id)
    else:
        # 测试第一个账号
        test_exchange()
