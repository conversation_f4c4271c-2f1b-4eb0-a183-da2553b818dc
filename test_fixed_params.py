#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复参数后的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web_server import get_xiaomi_cookies, XiaomiExchanger, load_accounts_from_file, mask_user_id

def test_fixed_params():
    print("🧪 测试修复参数后的功能")
    print("=" * 60)
    
    # 加载账号
    accounts = load_accounts_from_file()
    if not accounts:
        print("❌ 未找到账号信息")
        return
    
    # 测试第二个账号（之前显示36.18天的那个）
    account = accounts[1]  # 305******427
    print(f"📱 测试账号: {account['phone']}")
    print(f"👤 用户ID: {mask_user_id(account['userId'])}")
    
    # 获取cookies
    print("\n🔐 获取cookies...")
    cookies = get_xiaomi_cookies(account['passToken'], account['userId'])
    
    if not cookies:
        print("❌ cookies获取失败")
        return
    
    print("✅ cookies获取成功")
    
    # 创建兑换器
    exchanger = XiaomiExchanger(cookies)
    
    # 查询用户信息
    print("\n📊 查询用户信息（使用修复后的参数）...")
    if exchanger.query_user_info():
        print(f"💎 总天数: {exchanger.total_days}")
        print(f"📜 兑换历史: {'已兑换过' if exchanger.has_exchanged_before else '从未兑换'}")
        
        # 检查是否可以兑换
        if exchanger.total_days >= 31:
            print(f"✅ 可以兑换（天数: {exchanger.total_days} >= 31）")
            
            # 测试兑换（如果用户确认）
            response = input("\n⚠️ 是否要测试兑换？这会消耗31天积分！(y/N): ")
            if response.lower() == 'y':
                test_phone = "***********"
                exchange_type = "tencent"
                
                print(f"🚀 开始兑换测试...")
                success, message, raw_response = exchanger.exchange_member(test_phone, exchange_type)
                
                print(f"\n📋 兑换结果:")
                print(f"✅ 成功: {success}")
                print(f"💬 消息: {message}")
                print(f"🔧 原始响应:")
                import json
                print(json.dumps(raw_response, indent=2, ensure_ascii=False))
            else:
                print("⏭️ 跳过兑换测试")
        else:
            print(f"❌ 不能兑换（天数: {exchanger.total_days} < 31）")
    else:
        print("❌ 用户信息查询失败")
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    test_fixed_params()
